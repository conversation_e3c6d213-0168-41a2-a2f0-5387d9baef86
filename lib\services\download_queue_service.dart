import 'dart:collection';

import 'package:flutter/foundation.dart';
import 'package:instagram_reels_downloader/models/reel_model.dart';
import 'package:instagram_reels_downloader/services/download_service.dart';
import 'package:instagram_reels_downloader/services/notification_service.dart';
import 'package:instagram_reels_downloader/services/storage_service.dart';

enum DownloadStatus { queued, downloading, completed, failed, canceled }

class DownloadTask {
  final ReelModel reel;
  DownloadStatus status;
  double progress;
  String? error;
  int? fileSize;
  int downloadSpeed = 0;
  int timeRemaining = 0;

  DownloadTask({
    required this.reel,
    this.status = DownloadStatus.queued,
    this.progress = 0.0,
    this.error,
    this.fileSize,
  });
}

class DownloadQueueService extends ChangeNotifier {
  final DownloadService _downloadService = DownloadService();
  final StorageService _storageService = StorageService();
  final NotificationService _notificationService = NotificationService();

  // Queue of download tasks
  final Queue<DownloadTask> _queue = Queue<DownloadTask>();

  // Currently active download task
  DownloadTask? _activeTask;

  // Flag to indicate if the queue is processing
  bool _isProcessing = false;

  // Variables for tracking download speed
  int _lastUpdateTime = 0;
  int _lastBytes = 0;

  // Getters
  List<DownloadTask> get queue => List.unmodifiable(_queue);
  DownloadTask? get activeTask => _activeTask;
  bool get isProcessing => _isProcessing;
  int get queueLength => _queue.length;
  bool get hasActiveDownload => _activeTask != null;

  // Add a reel to the download queue
  void addToQueue(ReelModel reel) {
    // Check if the reel is already in the queue
    if (_queue.any((task) => task.reel.id == reel.id) ||
        (_activeTask != null && _activeTask!.reel.id == reel.id)) {
      debugPrint('Reel already in queue: ${reel.id}');
      return;
    }

    // Create a new download task
    final task = DownloadTask(reel: reel);

    // Add to queue
    _queue.add(task);
    debugPrint('Added reel to queue: ${reel.id}');

    // Notify listeners
    notifyListeners();

    // Start processing the queue if not already processing
    if (!_isProcessing) {
      _processQueue();
    }
  }

  // Remove a task from the queue
  void removeFromQueue(String reelId) {
    // Remove from queue
    _queue.removeWhere((task) => task.reel.id == reelId);
    debugPrint('Removed reel from queue: $reelId');

    // Notify listeners
    notifyListeners();
  }

  // Cancel all downloads
  void cancelAll() {
    // Clear the queue
    _queue.clear();

    // Cancel active download
    if (_activeTask != null) {
      _activeTask!.status = DownloadStatus.canceled;
      _activeTask = null;
    }

    // Reset processing flag
    _isProcessing = false;

    // Notify listeners
    notifyListeners();

    debugPrint('Canceled all downloads');
  }

  // Process the download queue
  Future<void> _processQueue() async {
    if (_isProcessing || _queue.isEmpty) {
      return;
    }

    _isProcessing = true;
    notifyListeners();

    while (_queue.isNotEmpty) {
      // Get the next task
      _activeTask = _queue.removeFirst();
      _activeTask!.status = DownloadStatus.downloading;

      // Notify listeners
      notifyListeners();

      try {
        // Download the reel
        final downloadedReel = await _downloadService.downloadReel(
          _activeTask!.reel,
          onProgress: (received, total) {
            if (total > 0) {
              // Update progress
              _activeTask!.progress = received / total;

              // Update file size
              _activeTask!.fileSize = total;

              // Calculate download speed
              final now = DateTime.now().millisecondsSinceEpoch;

              if (_lastUpdateTime > 0) {
                final timeDiff = now - _lastUpdateTime;
                if (timeDiff > 0) {
                  final bytesDiff = received - _lastBytes;
                  // Calculate speed in bytes per second
                  _activeTask!.downloadSpeed =
                      (bytesDiff * 1000 / timeDiff).round();

                  // Calculate time remaining
                  if (_activeTask!.downloadSpeed > 0) {
                    _activeTask!.timeRemaining =
                        ((total - received) / _activeTask!.downloadSpeed)
                            .round();
                  }
                }
              }

              _lastUpdateTime = now;
              _lastBytes = received;

              // Show notification
              _notificationService.showDownloadProgressNotification(
                title: 'Downloading Reel',
                progress: received,
                total: total,
                username: _activeTask!.reel.username,
              );

              // Notify listeners
              notifyListeners();
            }
          },
        );

        if (downloadedReel != null) {
          // Save to storage
          await _storageService.saveReel(downloadedReel);

          // Update task status
          _activeTask!.status = DownloadStatus.completed;
          _activeTask!.progress = 1.0;

          // Show completion notification
          _notificationService.showDownloadCompletedNotification(
            reel: downloadedReel,
            success: true,
          );

          debugPrint('Download completed: ${_activeTask!.reel.id}');
        } else {
          // Update task status
          _activeTask!.status = DownloadStatus.failed;
          _activeTask!.error = 'Download failed';

          // Show failure notification
          _notificationService.showDownloadCompletedNotification(
            reel: _activeTask!.reel,
            success: false,
            errorMessage: 'Download failed',
          );

          debugPrint('Download failed: ${_activeTask!.reel.id}');
        }
      } catch (e) {
        // Update task status
        _activeTask!.status = DownloadStatus.failed;
        _activeTask!.error = e.toString();

        // Show error notification
        _notificationService.showDownloadCompletedNotification(
          reel: _activeTask!.reel,
          success: false,
          errorMessage: e.toString(),
        );

        debugPrint('Download error: ${e.toString()}');
      } finally {
        // Notify listeners
        notifyListeners();

        // Reset active task
        _activeTask = null;
      }
    }

    _isProcessing = false;
    notifyListeners();
  }
}
