import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:instagram_reels_downloader/services/instagram_service.dart';

class ClipboardUtil {
  static final InstagramService _instagramService = InstagramService();

  // Get Instagram URL from clipboard
  static Future<String?> getInstagramUrlFromClipboard() async {
    // Skip clipboard access in web to avoid security errors
    if (kIsWeb) {
      print(
          'Clipboard access is limited in web. Please paste the URL manually.');
      return null;
    }

    try {
      final clipboardData = await Clipboard.getData(Clipboard.kTextPlain);
      final clipboardText = clipboardData?.text;

      if (clipboardText != null &&
          clipboardText.isNotEmpty &&
          _instagramService.isValidInstagramUrl(clipboardText)) {
        return clipboardText;
      }

      return null;
    } catch (e) {
      print('Error getting clipboard data: $e');
      return null;
    }
  }

  // Copy text to clipboard
  static Future<bool> copyToClipboard(String text) async {
    try {
      await Clipboard.setData(ClipboardData(text: text));
      return true;
    } catch (e) {
      print('Error copying to clipboard: $e');
      return false;
    }
  }
}
