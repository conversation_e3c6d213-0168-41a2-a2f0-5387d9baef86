import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:instagram_reels_downloader/models/reel_model.dart';
import 'package:instagram_reels_downloader/services/sharing_service.dart';

class ShareOptionsWidget extends StatelessWidget {
  final ReelModel reel;

  const ShareOptionsWidget({Key? key, required this.reel}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              const Icon(Icons.share, size: 24),
              const SizedBox(width: 8),
              const Text(
                'Share Reel',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              IconButton(
                icon: const Icon(Icons.close),
                onPressed: () => Navigator.pop(context),
              ),
            ],
          ),
          const Divider(),

          // Sharing options
          const SizedBox(height: 8),
          const Text(
            'Share to:',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 16),

          // Sharing platforms grid
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 4,
            mainAxisSpacing: 16,
            crossAxisSpacing: 16,
            children: [
              _buildSharingOption(
                context,
                'WhatsApp',
                Icons.message,
                Colors.green,
                () => _shareToWhatsApp(context),
              ),
              _buildSharingOption(
                context,
                'Facebook',
                Icons.thumb_up,
                Colors.blue,
                () => _shareToFacebook(context),
              ),
              _buildSharingOption(
                context,
                'Twitter',
                Icons.chat,
                Colors.lightBlue,
                () => _shareToTwitter(context),
              ),
              _buildSharingOption(
                context,
                'Telegram',
                Icons.send,
                Colors.blue,
                () => _shareToTelegram(context),
              ),
              _buildSharingOption(
                context,
                'Email',
                Icons.email,
                Colors.red,
                () => _shareViaEmail(context),
              ),
              _buildSharingOption(
                context,
                'Copy Link',
                Icons.link,
                Colors.grey,
                () => _copyVideoUrl(context),
              ),
              _buildSharingOption(
                context,
                'More',
                Icons.more_horiz,
                Colors.purple,
                () => _shareWithDefaultOptions(context),
              ),
            ],
          ),

          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildSharingOption(
    BuildContext context,
    String name,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: color,
              size: 28,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            name,
            style: const TextStyle(fontSize: 12),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // Share with default options
  void _shareWithDefaultOptions(BuildContext context) async {
    Navigator.pop(context);
    await SharingService().shareReel(reel);
  }

  // Share to WhatsApp
  void _shareToWhatsApp(BuildContext context) async {
    Navigator.pop(context);
    await SharingService().shareReelToPlatform(reel, SharingPlatform.whatsapp);
  }

  // Share to Facebook
  void _shareToFacebook(BuildContext context) async {
    Navigator.pop(context);
    await SharingService().shareReelToPlatform(reel, SharingPlatform.facebook);
  }

  // Share to Twitter
  void _shareToTwitter(BuildContext context) async {
    Navigator.pop(context);
    await SharingService().shareReelToPlatform(reel, SharingPlatform.twitter);
  }

  // Share to Telegram
  void _shareToTelegram(BuildContext context) async {
    Navigator.pop(context);
    await SharingService().shareReelToPlatform(reel, SharingPlatform.telegram);
  }

  // Share via email
  void _shareViaEmail(BuildContext context) async {
    Navigator.pop(context);
    await SharingService().shareReelToPlatform(reel, SharingPlatform.email);
  }

  // Copy video URL to clipboard
  void _copyVideoUrl(BuildContext context) async {
    await Clipboard.setData(ClipboardData(text: reel.videoUrl));
    Navigator.pop(context);

    // Show snackbar
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Video URL copied to clipboard'),
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  // Show the share options bottom sheet
  static void show(BuildContext context, ReelModel reel) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => ShareOptionsWidget(reel: reel),
    );
  }
}
