import 'dart:io';

import 'package:chewie/chewie.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:instagram_reels_downloader/models/reel_model.dart';
import 'package:video_player/video_player.dart';

class FullScreenVideoPlayerScreen extends StatefulWidget {
  final List<ReelModel> reels;
  final int initialIndex;

  const FullScreenVideoPlayerScreen({
    Key? key,
    required this.reels,
    required this.initialIndex,
  }) : super(key: key);

  @override
  State<FullScreenVideoPlayerScreen> createState() =>
      _FullScreenVideoPlayerScreenState();
}

class _FullScreenVideoPlayerScreenState
    extends State<FullScreenVideoPlayerScreen> {
  late PageController _pageController;
  late int _currentIndex;
  final Map<int, VideoPlayerController> _videoControllers = {};
  final Map<int, ChewieController> _chewieControllers = {};
  final Map<int, bool> _isInitialized = {};
  final Map<int, String> _errorMessages = {};

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: _currentIndex);

    // Set preferred orientations to landscape
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);

    // Enter full screen mode
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);

    // Initialize the current video and preload adjacent videos
    _initializeVideoPlayer(_currentIndex);
    if (_currentIndex > 0) {
      _initializeVideoPlayer(_currentIndex - 1);
    }
    if (_currentIndex < widget.reels.length - 1) {
      _initializeVideoPlayer(_currentIndex + 1);
    }
  }

  @override
  void dispose() {
    // Dispose all controllers
    for (final controller in _videoControllers.values) {
      controller.dispose();
    }
    for (final controller in _chewieControllers.values) {
      controller.dispose();
    }

    // Reset system UI and orientation
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
    ]);

    _pageController.dispose();
    super.dispose();
  }

  Future<void> _initializeVideoPlayer(int index) async {
    if (index < 0 ||
        index >= widget.reels.length ||
        _videoControllers.containsKey(index)) {
      return;
    }

    try {
      // Handle web platform differently
      if (kIsWeb) {
        setState(() {
          _errorMessages[index] =
              'Video playback is not available in web demo.';
        });
        return;
      }

      final reel = widget.reels[index];
      final file = File(reel.downloadPath);

      if (await file.exists()) {
        // Check if this is a placeholder file by checking file size
        final fileSize = await file.length();

        // If file is very small (less than 10KB), it might be a placeholder text file
        if (fileSize < 10 * 1024) {
          try {
            final content = await file.readAsString();
            if (content.contains('This is a placeholder')) {
              setState(() {
                _errorMessages[index] =
                    'This is a simulated download from a third-party API.';
              });
              return;
            }
          } catch (e) {
            debugPrint('Could not read file as string, trying as video: $e');
          }
        }

        // This is a real video file
        final videoController = VideoPlayerController.file(file);
        _videoControllers[index] = videoController;

        await videoController.initialize();

        if (videoController.value.duration.inMilliseconds == 0) {
          setState(() {
            _errorMessages[index] = 'Invalid video file or unsupported format.';
          });
          return;
        }

        // Create the chewie controller
        final chewieController = ChewieController(
          videoPlayerController: videoController,
          autoPlay: index == _currentIndex, // Only autoplay the current video
          looping: true,
          aspectRatio: videoController.value.aspectRatio > 0
              ? videoController.value.aspectRatio
              : 16 / 9,
          errorBuilder: (context, errorMessage) {
            return Center(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  'Error playing video: $errorMessage',
                  style: const TextStyle(color: Colors.white),
                  textAlign: TextAlign.center,
                ),
              ),
            );
          },
          allowFullScreen: true,
          allowMuting: true,
          showControls: true,
          placeholder: const Center(
            child: CircularProgressIndicator(),
          ),
        );

        _chewieControllers[index] = chewieController;

        setState(() {
          _isInitialized[index] = true;
        });
      } else {
        setState(() {
          _errorMessages[index] = 'Video file not found';
        });
      }
    } catch (e) {
      setState(() {
        _errorMessages[index] = 'Error initializing video player: $e';
      });
      debugPrint('Error initializing video player: $e');
    }
  }

  void _onPageChanged(int index) {
    setState(() {
      _currentIndex = index;
    });

    // Pause the previous video
    if (_videoControllers.containsKey(_currentIndex - 1)) {
      _videoControllers[_currentIndex - 1]?.pause();
    }

    // Play the current video
    if (_videoControllers.containsKey(_currentIndex)) {
      _videoControllers[_currentIndex]?.play();
    }

    // Preload the next video if it's not already loaded
    if (index < widget.reels.length - 1 &&
        !_videoControllers.containsKey(index + 1)) {
      _initializeVideoPlayer(index + 1);
    }

    // Preload the previous video if it's not already loaded
    if (index > 0 && !_videoControllers.containsKey(index - 1)) {
      _initializeVideoPlayer(index - 1);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: PageView.builder(
        controller: _pageController,
        itemCount: widget.reels.length,
        onPageChanged: _onPageChanged,
        itemBuilder: (context, index) {
          return GestureDetector(
            onTap: () {
              // Toggle app bar visibility
              SystemChrome.setEnabledSystemUIMode(
                SystemUiMode.immersiveSticky,
                overlays: [],
              );
            },
            child: _buildVideoPlayer(index),
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        mini: true,
        backgroundColor: Colors.black.withOpacity(0.5),
        child: const Icon(Icons.close, color: Colors.white),
        onPressed: () {
          Navigator.pop(context);
        },
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.endTop,
    );
  }

  Widget _buildVideoPlayer(int index) {
    if (_isInitialized[index] == true &&
        _chewieControllers.containsKey(index)) {
      return Chewie(controller: _chewieControllers[index]!);
    } else if (_errorMessages.containsKey(index)) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Text(
            _errorMessages[index]!,
            style: const TextStyle(color: Colors.white),
            textAlign: TextAlign.center,
          ),
        ),
      );
    } else {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }
  }
}
