import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:instagram_reels_downloader/models/reel_model.dart';

class StorageService {
  static const String _reelsKey = 'downloaded_reels';
  
  // Save a downloaded reel to local storage
  Future<bool> saveReel(ReelModel reel) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Get existing reels
      final reels = await getDownloadedReels();
      
      // Add the new reel
      reels.add(reel);
      
      // Convert to JSON and save
      final reelsJson = reels.map((r) => r.toJson()).toList();
      await prefs.setString(_reelsKey, jsonEncode(reelsJson));
      
      return true;
    } catch (e) {
      print('Error saving reel: $e');
      return false;
    }
  }
  
  // Get all downloaded reels
  Future<List<ReelModel>> getDownloadedReels() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Get the stored JSON string
      final reelsJson = prefs.getString(_reelsKey);
      
      if (reelsJson == null || reelsJson.isEmpty) {
        return [];
      }
      
      // Parse the JSON and convert to ReelModel objects
      final List<dynamic> decodedJson = jsonDecode(reelsJson);
      final reels = decodedJson
          .map((json) => ReelModel.fromJson(json))
          .toList();
      
      // Sort by download date (newest first)
      reels.sort((a, b) => b.downloadDate.compareTo(a.downloadDate));
      
      return reels;
    } catch (e) {
      print('Error getting downloaded reels: $e');
      return [];
    }
  }
  
  // Delete a reel from storage
  Future<bool> deleteReel(String reelId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Get existing reels
      final reels = await getDownloadedReels();
      
      // Remove the reel with the given ID
      reels.removeWhere((reel) => reel.id == reelId);
      
      // Convert to JSON and save
      final reelsJson = reels.map((r) => r.toJson()).toList();
      await prefs.setString(_reelsKey, jsonEncode(reelsJson));
      
      return true;
    } catch (e) {
      print('Error deleting reel: $e');
      return false;
    }
  }
  
  // Clear all downloaded reels
  Future<bool> clearDownloadedReels() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_reelsKey);
      return true;
    } catch (e) {
      print('Error clearing downloaded reels: $e');
      return false;
    }
  }
}
