import 'package:flutter/material.dart';
import 'package:instagram_reels_downloader/providers/download_queue_provider.dart';
import 'package:instagram_reels_downloader/services/download_queue_service.dart';
import 'package:provider/provider.dart';

class DownloadQueueScreen extends StatelessWidget {
  const DownloadQueueScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Download Queue'),
        actions: [
          IconButton(
            icon: const Icon(Icons.clear_all),
            tooltip: 'Cancel All',
            onPressed: () {
              _showCancelAllDialog(context);
            },
          ),
        ],
      ),
      body: Consumer<DownloadQueueProvider>(
        builder: (context, provider, child) {
          // Show active download
          final activeTask = provider.activeTask;
          final queueItems = provider.queue;
          
          if (activeTask == null && queueItems.isEmpty) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.queue,
                    size: 64,
                    color: Colors.grey,
                  ),
                  SizedBox(height: 16),
                  Text(
                    'No active downloads',
                    style: TextStyle(
                      fontSize: 18,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            );
          }
          
          return ListView(
            padding: const EdgeInsets.all(16.0),
            children: [
              // Active download
              if (activeTask != null) ...[
                _buildSectionHeader(context, 'Active Download'),
                const SizedBox(height: 8),
                _buildDownloadTaskCard(context, activeTask, isActive: true),
                const SizedBox(height: 16),
              ],
              
              // Queue
              if (queueItems.isNotEmpty) ...[
                _buildSectionHeader(context, 'Queue (${queueItems.length})'),
                const SizedBox(height: 8),
                ...queueItems.map((task) => Padding(
                  padding: const EdgeInsets.only(bottom: 8.0),
                  child: _buildDownloadTaskCard(context, task),
                )),
              ],
            ],
          );
        },
      ),
    );
  }
  
  Widget _buildSectionHeader(BuildContext context, String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.bold,
          color: Theme.of(context).colorScheme.primary,
        ),
      ),
    );
  }
  
  Widget _buildDownloadTaskCard(
    BuildContext context, 
    DownloadTask task, 
    {bool isActive = false}
  ) {
    final colorScheme = Theme.of(context).colorScheme;
    
    // Determine status color and icon
    Color statusColor;
    IconData statusIcon;
    
    switch (task.status) {
      case DownloadStatus.queued:
        statusColor = Colors.grey;
        statusIcon = Icons.queue;
        break;
      case DownloadStatus.downloading:
        statusColor = Colors.blue;
        statusIcon = Icons.download;
        break;
      case DownloadStatus.completed:
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        break;
      case DownloadStatus.failed:
        statusColor = Colors.red;
        statusIcon = Icons.error;
        break;
      case DownloadStatus.canceled:
        statusColor = Colors.orange;
        statusIcon = Icons.cancel;
        break;
    }
    
    return Card(
      elevation: isActive ? 4 : 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: isActive 
            ? BorderSide(color: colorScheme.primary, width: 2) 
            : BorderSide.none,
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Username and status
            Row(
              children: [
                Icon(statusIcon, color: statusColor),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    '@${task.reel.username}',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                if (!isActive && task.status == DownloadStatus.queued)
                  IconButton(
                    icon: const Icon(Icons.remove_circle_outline, color: Colors.red),
                    tooltip: 'Remove from queue',
                    onPressed: () {
                      Provider.of<DownloadQueueProvider>(context, listen: false)
                          .removeFromQueue(task.reel.id);
                    },
                  ),
              ],
            ),
            
            // Progress bar (for active downloads)
            if (task.status == DownloadStatus.downloading) ...[
              const SizedBox(height: 12),
              LinearProgressIndicator(
                value: task.progress,
                backgroundColor: Colors.grey.shade200,
                valueColor: AlwaysStoppedAnimation<Color>(statusColor),
                minHeight: 8,
                borderRadius: BorderRadius.circular(4),
              ),
              const SizedBox(height: 8),
              
              // Download metrics
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Progress percentage
                  Text(
                    '${(task.progress * 100).toStringAsFixed(0)}%',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  
                  // Speed
                  if (task.downloadSpeed > 0)
                    Text(
                      _formatSpeed(task.downloadSpeed),
                      style: TextStyle(color: Colors.grey.shade700, fontSize: 12),
                    ),
                  
                  // Time remaining
                  if (task.timeRemaining > 0)
                    Text(
                      _formatTimeRemaining(task.timeRemaining),
                      style: TextStyle(color: Colors.grey.shade700, fontSize: 12),
                    ),
                ],
              ),
            ],
            
            // Thumbnail and caption
            const SizedBox(height: 12),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Thumbnail
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.network(
                    task.reel.thumbnailUrl,
                    width: 80,
                    height: 80,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        width: 80,
                        height: 80,
                        color: Colors.grey.shade300,
                        child: const Icon(Icons.image_not_supported),
                      );
                    },
                  ),
                ),
                const SizedBox(width: 12),
                
                // Caption
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        task.reel.caption,
                        maxLines: 3,
                        overflow: TextOverflow.ellipsis,
                        style: const TextStyle(fontSize: 14),
                      ),
                      if (task.status == DownloadStatus.failed && task.error != null) ...[
                        const SizedBox(height: 8),
                        Text(
                          'Error: ${task.error}',
                          style: TextStyle(color: Colors.red.shade700, fontSize: 12),
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
  
  String _formatSpeed(int bytesPerSecond) {
    if (bytesPerSecond < 1024) {
      return '$bytesPerSecond B/s';
    } else if (bytesPerSecond < 1024 * 1024) {
      return '${(bytesPerSecond / 1024).toStringAsFixed(1)} KB/s';
    } else {
      return '${(bytesPerSecond / (1024 * 1024)).toStringAsFixed(1)} MB/s';
    }
  }
  
  String _formatTimeRemaining(int seconds) {
    if (seconds <= 0) {
      return 'Calculating...';
    } else if (seconds < 60) {
      return '$seconds sec';
    } else if (seconds < 3600) {
      final minutes = (seconds / 60).floor();
      final remainingSeconds = seconds % 60;
      return '$minutes min ${remainingSeconds > 0 ? "$remainingSeconds sec" : ""}';
    } else {
      final hours = (seconds / 3600).floor();
      final minutes = ((seconds % 3600) / 60).floor();
      return '$hours hr ${minutes > 0 ? "$minutes min" : ""}';
    }
  }
  
  void _showCancelAllDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cancel All Downloads'),
        content: const Text(
          'Are you sure you want to cancel all downloads in the queue?'
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('No'),
          ),
          TextButton(
            onPressed: () {
              Provider.of<DownloadQueueProvider>(context, listen: false)
                  .cancelAll();
              Navigator.pop(context);
            },
            child: const Text('Yes'),
          ),
        ],
      ),
    );
  }
}
