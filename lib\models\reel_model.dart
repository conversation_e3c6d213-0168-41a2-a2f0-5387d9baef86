class ReelModel {
  final String id;
  final String videoUrl;
  final String thumbnailUrl;
  final String username;
  final String caption;
  final String downloadPath;
  final DateTime downloadDate;
  final int? fileSize; // File size in bytes
  final String downloadStatus; // 'queued', 'downloading', 'completed', 'failed'

  ReelModel({
    required this.id,
    required this.videoUrl,
    required this.thumbnailUrl,
    required this.username,
    required this.caption,
    required this.downloadPath,
    required this.downloadDate,
    this.fileSize,
    this.downloadStatus = 'completed',
  });

  factory ReelModel.fromJson(Map<String, dynamic> json) {
    return ReelModel(
      id: json['id'] ?? '',
      videoUrl: json['videoUrl'] ?? '',
      thumbnailUrl: json['thumbnailUrl'] ?? '',
      username: json['username'] ?? '',
      caption: json['caption'] ?? '',
      downloadPath: json['downloadPath'] ?? '',
      downloadDate: json['downloadDate'] != null
          ? DateTime.parse(json['downloadDate'])
          : DateTime.now(),
      fileSize: json['fileSize'] != null
          ? int.parse(json['fileSize'].toString())
          : null,
      downloadStatus: json['downloadStatus'] ?? 'completed',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'videoUrl': videoUrl,
      'thumbnailUrl': thumbnailUrl,
      'username': username,
      'caption': caption,
      'downloadPath': downloadPath,
      'downloadDate': downloadDate.toIso8601String(),
      'fileSize': fileSize,
      'downloadStatus': downloadStatus,
    };
  }
}
