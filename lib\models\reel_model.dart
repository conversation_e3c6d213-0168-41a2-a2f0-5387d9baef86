class ReelModel {
  final String id;
  final String videoUrl;
  final String thumbnailUrl;
  final String username;
  final String caption;
  final String downloadPath;
  final DateTime downloadDate;
  final int? fileSize; // File size in bytes
  final String downloadStatus; // 'queued', 'downloading', 'completed', 'failed'
  final Duration? videoDuration; // Video duration
  final String? videoQuality; // Video quality (e.g., '720p', '1080p')
  final int? viewCount; // Number of views
  final int? likeCount; // Number of likes
  final bool isVerified; // Is the user verified
  final String? profilePicUrl; // User's profile picture URL
  final List<String> hashtags; // Hashtags in the caption
  final DateTime? originalPostDate; // Original post date from Instagram
  final String? musicTitle; // Background music title
  final String? musicArtist; // Background music artist
  final Map<String, dynamic>? metadata; // Additional metadata

  ReelModel({
    required this.id,
    required this.videoUrl,
    required this.thumbnailUrl,
    required this.username,
    required this.caption,
    required this.downloadPath,
    required this.downloadDate,
    this.fileSize,
    this.downloadStatus = 'completed',
    this.videoDuration,
    this.videoQuality,
    this.viewCount,
    this.likeCount,
    this.isVerified = false,
    this.profilePicUrl,
    this.hashtags = const [],
    this.originalPostDate,
    this.musicTitle,
    this.musicArtist,
    this.metadata,
  });

  factory ReelModel.fromJson(Map<String, dynamic> json) {
    return ReelModel(
      id: json['id'] ?? '',
      videoUrl: json['videoUrl'] ?? '',
      thumbnailUrl: json['thumbnailUrl'] ?? '',
      username: json['username'] ?? '',
      caption: json['caption'] ?? '',
      downloadPath: json['downloadPath'] ?? '',
      downloadDate: json['downloadDate'] != null
          ? DateTime.parse(json['downloadDate'])
          : DateTime.now(),
      fileSize: json['fileSize'] != null
          ? int.parse(json['fileSize'].toString())
          : null,
      downloadStatus: json['downloadStatus'] ?? 'completed',
      videoDuration: json['videoDuration'] != null
          ? Duration(seconds: json['videoDuration'])
          : null,
      videoQuality: json['videoQuality'],
      viewCount: json['viewCount'],
      likeCount: json['likeCount'],
      isVerified: json['isVerified'] ?? false,
      profilePicUrl: json['profilePicUrl'],
      hashtags:
          json['hashtags'] != null ? List<String>.from(json['hashtags']) : [],
      originalPostDate: json['originalPostDate'] != null
          ? DateTime.parse(json['originalPostDate'])
          : null,
      musicTitle: json['musicTitle'],
      musicArtist: json['musicArtist'],
      metadata: json['metadata'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'videoUrl': videoUrl,
      'thumbnailUrl': thumbnailUrl,
      'username': username,
      'caption': caption,
      'downloadPath': downloadPath,
      'downloadDate': downloadDate.toIso8601String(),
      'fileSize': fileSize,
      'downloadStatus': downloadStatus,
      'videoDuration': videoDuration?.inSeconds,
      'videoQuality': videoQuality,
      'viewCount': viewCount,
      'likeCount': likeCount,
      'isVerified': isVerified,
      'profilePicUrl': profilePicUrl,
      'hashtags': hashtags,
      'originalPostDate': originalPostDate?.toIso8601String(),
      'musicTitle': musicTitle,
      'musicArtist': musicArtist,
      'metadata': metadata,
    };
  }

  /// Create a copy of this reel with updated fields
  ReelModel copyWith({
    String? id,
    String? videoUrl,
    String? thumbnailUrl,
    String? username,
    String? caption,
    String? downloadPath,
    DateTime? downloadDate,
    int? fileSize,
    String? downloadStatus,
    Duration? videoDuration,
    String? videoQuality,
    int? viewCount,
    int? likeCount,
    bool? isVerified,
    String? profilePicUrl,
    List<String>? hashtags,
    DateTime? originalPostDate,
    String? musicTitle,
    String? musicArtist,
    Map<String, dynamic>? metadata,
  }) {
    return ReelModel(
      id: id ?? this.id,
      videoUrl: videoUrl ?? this.videoUrl,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      username: username ?? this.username,
      caption: caption ?? this.caption,
      downloadPath: downloadPath ?? this.downloadPath,
      downloadDate: downloadDate ?? this.downloadDate,
      fileSize: fileSize ?? this.fileSize,
      downloadStatus: downloadStatus ?? this.downloadStatus,
      videoDuration: videoDuration ?? this.videoDuration,
      videoQuality: videoQuality ?? this.videoQuality,
      viewCount: viewCount ?? this.viewCount,
      likeCount: likeCount ?? this.likeCount,
      isVerified: isVerified ?? this.isVerified,
      profilePicUrl: profilePicUrl ?? this.profilePicUrl,
      hashtags: hashtags ?? this.hashtags,
      originalPostDate: originalPostDate ?? this.originalPostDate,
      musicTitle: musicTitle ?? this.musicTitle,
      musicArtist: musicArtist ?? this.musicArtist,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Get formatted file size
  String get formattedFileSize {
    if (fileSize == null) return 'Unknown';

    final bytes = fileSize!;
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024)
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// Get formatted video duration
  String get formattedDuration {
    if (videoDuration == null) return 'Unknown';

    final duration = videoDuration!;
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds.remainder(60);

    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  /// Check if the reel is downloaded
  bool get isDownloaded => downloadStatus == 'completed';

  /// Check if the reel is currently downloading
  bool get isDownloading => downloadStatus == 'downloading';

  /// Check if the download failed
  bool get isDownloadFailed => downloadStatus == 'failed';

  /// Check if the reel is in queue
  bool get isQueued => downloadStatus == 'queued';
}
