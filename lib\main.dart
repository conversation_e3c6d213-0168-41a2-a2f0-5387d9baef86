import 'package:flutter/material.dart';
import 'package:instagram_reels_downloader/providers/download_queue_provider.dart';
import 'package:instagram_reels_downloader/providers/reels_provider.dart';
import 'package:instagram_reels_downloader/providers/theme_provider.dart';
import 'package:instagram_reels_downloader/screens/splash_screen.dart';
import 'package:instagram_reels_downloader/services/notification_service.dart';
import 'package:provider/provider.dart';

void main() async {
  // Ensure Flutter is initialized
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize notification service
  await NotificationService().initialize();

  // Create and initialize providers
  final downloadQueueProvider = DownloadQueueProvider();
  await downloadQueueProvider.initialize();

  runApp(MyApp(downloadQueueProvider: downloadQueueProvider));
}

class MyApp extends StatelessWidget {
  final DownloadQueueProvider downloadQueueProvider;

  const MyApp({super.key, required this.downloadQueueProvider});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => ReelsProvider()),
        ChangeNotifierProvider(create: (context) => ThemeProvider()),
        ChangeNotifierProvider.value(value: downloadQueueProvider),
      ],
      child: Consumer<ThemeProvider>(
        builder: (context, themeProvider, child) {
          return MaterialApp(
            title: 'Instagram Reels Downloader',
            theme: themeProvider.getLightTheme(),
            darkTheme: themeProvider.getDarkTheme(),
            themeMode: themeProvider.themeMode,
            home: const SplashScreen(),
            debugShowCheckedModeBanner: false,
          );
        },
      ),
    );
  }
}
