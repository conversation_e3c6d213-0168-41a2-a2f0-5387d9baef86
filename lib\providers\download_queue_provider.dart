import 'package:flutter/foundation.dart';
import 'package:instagram_reels_downloader/models/reel_model.dart';
import 'package:instagram_reels_downloader/services/download_queue_service.dart';
import 'package:instagram_reels_downloader/services/storage_service.dart';

class DownloadQueueProvider extends ChangeNotifier {
  final DownloadQueueService _queueService = DownloadQueueService();
  final StorageService _storageService = StorageService();
  
  // Getters
  List<DownloadTask> get queue => _queueService.queue;
  DownloadTask? get activeTask => _queueService.activeTask;
  bool get isProcessing => _queueService.isProcessing;
  int get queueLength => _queueService.queueLength;
  bool get hasActiveDownload => _queueService.hasActiveDownload;
  
  // Initialize the provider
  Future<void> initialize() async {
    // Load downloaded reels
    await loadDownloadedReels();
    
    // Listen to changes in the queue service
    _queueService.addListener(_onQueueServiceChanged);
  }
  
  // Handle changes in the queue service
  void _onQueueServiceChanged() {
    notifyListeners();
  }
  
  // Add a reel to the download queue
  void addToQueue(ReelModel reel) {
    _queueService.addToQueue(reel);
  }
  
  // Remove a task from the queue
  void removeFromQueue(String reelId) {
    _queueService.removeFromQueue(reelId);
  }
  
  // Cancel all downloads
  void cancelAll() {
    _queueService.cancelAll();
  }
  
  // Load downloaded reels
  Future<List<ReelModel>> loadDownloadedReels() async {
    try {
      final reels = await _storageService.getDownloadedReels();
      return reels;
    } catch (e) {
      debugPrint('Error loading downloaded reels: $e');
      return [];
    }
  }
  
  // Delete a downloaded reel
  Future<void> deleteReel(String reelId) async {
    try {
      await _storageService.deleteReel(reelId);
    } catch (e) {
      debugPrint('Error deleting reel: $e');
    }
  }
  
  // Clear all downloaded reels
  Future<void> clearDownloadedReels() async {
    try {
      await _storageService.clearDownloadedReels();
    } catch (e) {
      debugPrint('Error clearing downloaded reels: $e');
    }
  }
  
  @override
  void dispose() {
    _queueService.removeListener(_onQueueServiceChanged);
    super.dispose();
  }
}
