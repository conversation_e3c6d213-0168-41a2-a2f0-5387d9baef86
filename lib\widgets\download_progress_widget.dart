import 'package:flutter/material.dart';
import 'package:instagram_reels_downloader/providers/reels_provider.dart';
import 'package:provider/provider.dart';

class DownloadProgressWidget extends StatelessWidget {
  const DownloadProgressWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<ReelsProvider>(
      builder: (context, provider, child) {
        if (!provider.isDownloading) {
          return const SizedBox.shrink();
        }

        // Format download speed
        String speedText = _formatSpeed(provider.downloadSpeed);
        
        // Format file size
        String sizeText = _formatFileSize(provider.totalBytes);
        String downloadedText = _formatFileSize(provider.downloadedBytes);
        
        // Format time remaining
        String timeText = _formatTimeRemaining(provider.timeRemaining);

        // Get status text
        String statusText = _getStatusText(provider.downloadStatus);

        return Card(
          elevation: 4,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // Status and progress
                Row(
                  children: [
                    Icon(
                      _getStatusIcon(provider.downloadStatus),
                      color: _getStatusColor(provider.downloadStatus),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      statusText,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: _getStatusColor(provider.downloadStatus),
                      ),
                    ),
                    const Spacer(),
                    Text(
                      '${(provider.downloadProgress * 100).toStringAsFixed(0)}%',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                
                // Progress bar
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: LinearProgressIndicator(
                    value: provider.downloadProgress,
                    backgroundColor: Colors.grey.shade200,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      _getStatusColor(provider.downloadStatus),
                    ),
                    minHeight: 10,
                  ),
                ),
                const SizedBox(height: 16),
                
                // Download metrics
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _buildMetricItem(
                      context,
                      Icons.speed,
                      'Speed',
                      speedText,
                    ),
                    _buildMetricItem(
                      context,
                      Icons.sd_card,
                      'Size',
                      '$downloadedText / $sizeText',
                    ),
                    _buildMetricItem(
                      context,
                      Icons.timer,
                      'Time Left',
                      timeText,
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildMetricItem(
    BuildContext context,
    IconData icon,
    String label,
    String value,
  ) {
    return Column(
      children: [
        Icon(icon, size: 20, color: Colors.grey.shade700),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade700,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 14,
          ),
        ),
      ],
    );
  }

  String _formatSpeed(int bytesPerSecond) {
    if (bytesPerSecond < 1024) {
      return '$bytesPerSecond B/s';
    } else if (bytesPerSecond < 1024 * 1024) {
      return '${(bytesPerSecond / 1024).toStringAsFixed(1)} KB/s';
    } else {
      return '${(bytesPerSecond / (1024 * 1024)).toStringAsFixed(1)} MB/s';
    }
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }

  String _formatTimeRemaining(int seconds) {
    if (seconds <= 0) {
      return 'Calculating...';
    } else if (seconds < 60) {
      return '$seconds sec';
    } else if (seconds < 3600) {
      final minutes = (seconds / 60).floor();
      final remainingSeconds = seconds % 60;
      return '$minutes min ${remainingSeconds > 0 ? "$remainingSeconds sec" : ""}';
    } else {
      final hours = (seconds / 3600).floor();
      final minutes = ((seconds % 3600) / 60).floor();
      return '$hours hr ${minutes > 0 ? "$minutes min" : ""}';
    }
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'preparing':
        return 'Preparing Download';
      case 'downloading':
        return 'Downloading';
      case 'completed':
        return 'Download Complete';
      case 'failed':
        return 'Download Failed';
      default:
        return 'Ready';
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status) {
      case 'preparing':
        return Icons.pending;
      case 'downloading':
        return Icons.download_rounded;
      case 'completed':
        return Icons.check_circle;
      case 'failed':
        return Icons.error;
      default:
        return Icons.download;
    }
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'preparing':
        return Colors.orange;
      case 'downloading':
        return Colors.blue;
      case 'completed':
        return Colors.green;
      case 'failed':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}
