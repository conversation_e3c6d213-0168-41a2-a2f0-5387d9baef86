// This file contains API configuration
// In a production app, these values should be loaded from environment variables
// or a secure storage solution, not hardcoded in the source code.

class ApiConfig {
  // RapidAPI key for Instagram Reels Downloader API
  // Replace this with your own API key from RapidAPI
  static const String rapidApiKey =
      '**************************************************';

  // API endpoints for Instagram Reels Downloader
  static const List<String> apiEndpoints = [
    // This is the primary API endpoint that has been tested and works
    'https://instagram-reels-downloader-api.p.rapidapi.com/download',
    // These are fallback endpoints if the primary one fails
    'https://instagram-downloader-download-instagram-videos-stories.p.rapidapi.com/index',
    'https://instagram-media-downloader.p.rapidapi.com/rapid/post.php',
    'https://instagram-downloader-api.p.rapidapi.com/media',
  ];

  // Fallback video URLs for demonstration when API calls fail
  // These are sample videos that can be used for testing
  static const List<String> fallbackVideoUrls = [
    'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
    'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4',
    'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4',
    'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerEscapes.mp4',
    'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerFun.mp4',
    'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerJoyrides.mp4',
    'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerMeltdowns.mp4',
    'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/Sintel.mp4',
    'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/SubaruOutbackOnStreetAndDirt.mp4',
    'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/TearsOfSteel.mp4',
  ];

  // Get a random fallback video URL
  static String getRandomFallbackVideoUrl() {
    // Use the current timestamp to select a video
    final index =
        DateTime.now().millisecondsSinceEpoch % fallbackVideoUrls.length;
    return fallbackVideoUrls[index];
  }
}
