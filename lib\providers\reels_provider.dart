import 'package:flutter/material.dart';
import 'package:instagram_reels_downloader/models/reel_model.dart';
import 'package:instagram_reels_downloader/services/download_service.dart';
import 'package:instagram_reels_downloader/services/instagram_service.dart';
import 'package:instagram_reels_downloader/services/storage_service.dart';

class ReelsProvider extends ChangeNotifier {
  final InstagramService _instagramService = InstagramService();
  final DownloadService _downloadService = DownloadService();
  final StorageService _storageService = StorageService();

  List<ReelModel> _downloadedReels = [];
  ReelModel? _currentReel;
  bool _isLoading = false;
  String _errorMessage = '';
  bool _isDownloading = false;
  double _downloadProgress = 0.0;
  int _downloadSpeed = 0; // bytes per second
  int _totalBytes = 0; // total file size in bytes
  int _downloadedBytes = 0; // downloaded bytes so far
  int _timeRemaining = 0; // estimated time remaining in seconds
  String _downloadStatus = 'ready'; // current status message

  // Variables for tracking download speed
  int _lastUpdateTime = 0;
  int _lastBytes = 0;

  // Getters
  List<ReelModel> get downloadedReels => _downloadedReels;
  ReelModel? get currentReel => _currentReel;
  bool get isLoading => _isLoading;
  String get errorMessage => _errorMessage;
  bool get isDownloading => _isDownloading;
  double get downloadProgress => _downloadProgress;
  int get downloadSpeed => _downloadSpeed;
  int get totalBytes => _totalBytes;
  int get downloadedBytes => _downloadedBytes;
  int get timeRemaining => _timeRemaining;
  String get downloadStatus => _downloadStatus;

  // Initialize the provider
  Future<void> initialize() async {
    await loadDownloadedReels();
  }

  // Load downloaded reels from storage
  Future<void> loadDownloadedReels() async {
    _isLoading = true;
    notifyListeners();

    try {
      _downloadedReels = await _storageService.getDownloadedReels();
    } catch (e) {
      _errorMessage = 'Failed to load downloaded reels: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Fetch reel information from URL
  Future<void> fetchReelInfo(String url) async {
    _isLoading = true;
    _errorMessage = '';
    notifyListeners();

    try {
      final reel = await _instagramService.fetchReelInfo(url);

      if (reel != null) {
        _currentReel = reel;
      } else {
        _errorMessage = 'Failed to fetch reel information';
      }
    } catch (e) {
      _errorMessage = 'Error: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Download the current reel
  Future<bool> downloadCurrentReel() async {
    if (_currentReel == null) {
      _errorMessage = 'No reel to download';
      notifyListeners();
      return false;
    }

    _isDownloading = true;
    _downloadProgress = 0.0;
    _downloadSpeed = 0;
    _totalBytes = 0;
    _downloadedBytes = 0;
    _timeRemaining = 0;
    _downloadStatus = 'preparing';
    notifyListeners();

    try {
      // Check if the video URL is a direct URL, a mock URL, or an Instagram page URL
      if (!_instagramService.isDirectVideoUrl(_currentReel!.videoUrl) &&
          !_currentReel!.videoUrl.contains('mock-instagram-cdn.com') &&
          _currentReel!.videoUrl.contains('instagram.com/reel/')) {
        _errorMessage =
            'Unable to extract direct video URL from Instagram. Instagram may have changed their API or HTML structure.';
        _isDownloading = false;
        _downloadStatus = 'failed';
        notifyListeners();
        return false;
      }

      _downloadStatus = 'downloading';
      notifyListeners();

      // Download the reel with progress tracking
      final downloadedReel = await _downloadService.downloadReel(
        _currentReel!,
        onProgress: (received, total) {
          if (total > 0) {
            _downloadProgress = received / total;
            _totalBytes = total;
            _downloadedBytes = received;

            // Calculate download speed (simple moving average)
            final now = DateTime.now().millisecondsSinceEpoch;

            if (_lastUpdateTime > 0) {
              final timeDiff = now - _lastUpdateTime;
              if (timeDiff > 0) {
                final bytesDiff = received - _lastBytes;
                // Calculate speed in bytes per second
                _downloadSpeed = (bytesDiff * 1000 / timeDiff).round();

                // Calculate time remaining
                if (_downloadSpeed > 0) {
                  _timeRemaining =
                      ((total - received) / _downloadSpeed).round();
                }
              }
            }

            _lastUpdateTime = now;
            _lastBytes = received;

            notifyListeners();
          }
        },
      );

      if (downloadedReel != null) {
        // Save to storage
        await _storageService.saveReel(downloadedReel);

        // Update current reel
        _currentReel = downloadedReel;

        // Reload downloaded reels
        await loadDownloadedReels();

        _isDownloading = false;
        _downloadProgress = 1.0;
        _downloadStatus = 'completed';
        notifyListeners();
        return true;
      } else {
        _errorMessage =
            'Failed to download reel. Please try again with a different reel.';
        _isDownloading = false;
        _downloadStatus = 'failed';
        notifyListeners();
        return false;
      }
    } catch (e) {
      // Provide a more user-friendly error message
      if (e.toString().contains('Direct video URL not found')) {
        _errorMessage =
            'Unable to extract direct video URL from Instagram. Instagram may have changed their API or HTML structure.';
      } else if (e.toString().contains('SocketException')) {
        _errorMessage =
            'Network error. Please check your internet connection and try again.';
      } else if (e.toString().contains('Permission')) {
        _errorMessage =
            'Storage permission denied. Please grant permission to save videos.';
      } else {
        _errorMessage =
            'Error downloading reel: ${e.toString().split(':').first}';
      }

      _isDownloading = false;
      notifyListeners();
      return false;
    }
  }

  // Delete a downloaded reel
  Future<void> deleteReel(String reelId) async {
    try {
      await _storageService.deleteReel(reelId);
      await loadDownloadedReels();
    } catch (e) {
      _errorMessage = 'Error deleting reel: $e';
      notifyListeners();
    }
  }

  // Clear all downloaded reels
  Future<void> clearDownloadedReels() async {
    try {
      await _storageService.clearDownloadedReels();
      await loadDownloadedReels();
    } catch (e) {
      _errorMessage = 'Error clearing downloaded reels: $e';
      notifyListeners();
    }
  }

  // Reset current reel
  void resetCurrentReel() {
    _currentReel = null;
    notifyListeners();
  }

  // Reset error message
  void resetErrorMessage() {
    _errorMessage = '';
    notifyListeners();
  }
}
