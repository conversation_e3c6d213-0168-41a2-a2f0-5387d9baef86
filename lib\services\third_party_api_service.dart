import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:instagram_reels_downloader/config/api_config.dart';
import 'package:instagram_reels_downloader/models/reel_model.dart';

/// A service that uses third-party APIs to extract Instagram video URLs
/// This provides a fallback when direct extraction fails
class ThirdPartyApiService {
  // List of API endpoints to try
  final List<String> _apiEndpoints = ApiConfig.apiEndpoints;

  // RapidAPI key - loaded from the API config
  // You need to sign up for RapidAPI and get your own key from one of these services:
  // https://rapidapi.com/search/instagram
  final String _rapidApiKey = ApiConfig.rapidApiKey;

  /// Extract video URL using third-party APIs
  /// Returns a tuple of (videoUrl, apiData) where apiData is optional metadata from the API
  Future<Map<String, dynamic>> extractVideoUrl(String instagramUrl) async {
    debugPrint('Attempting to extract video URL from: $instagramUrl');
    Map<String, dynamic>? apiData;

    // The API key is already set in the code, so we'll use it
    debugPrint('Using RapidAPI key: ${_rapidApiKey.substring(0, 5)}...');

    // Try each API endpoint until one works
    for (final endpoint in _apiEndpoints) {
      try {
        debugPrint('Trying API endpoint: $endpoint');

        // Special handling for the instagram-reels-downloader-api endpoint
        if (endpoint.contains('instagram-reels-downloader-api')) {
          final result =
              await _makeReelsDownloaderApiRequest(endpoint, instagramUrl);
          if (result != null) {
            final videoUrl = result['videoUrl'] as String?;
            apiData = result['apiData'] as Map<String, dynamic>?;

            if (videoUrl != null && videoUrl.isNotEmpty) {
              debugPrint(
                  'Successfully extracted video URL using endpoint: $endpoint');
              debugPrint('Video URL: $videoUrl');
              return {'videoUrl': videoUrl, 'apiData': apiData};
            }
          }
        }
        // Standard handling for other endpoints
        else {
          final videoUrl = await _makeApiRequest(endpoint, instagramUrl);
          if (videoUrl != null && videoUrl.isNotEmpty) {
            debugPrint(
                'Successfully extracted video URL using endpoint: $endpoint');
            debugPrint('Video URL: $videoUrl');
            return {'videoUrl': videoUrl, 'apiData': null};
          }
        }
      } catch (e) {
        debugPrint('API endpoint $endpoint failed: $e');
        // Continue to the next endpoint
      }
    }

    // If all API endpoints fail, try the fallback method
    debugPrint('All API endpoints failed. Trying fallback method...');
    final videoUrl = await _extractWithoutApiKey(instagramUrl);

    if (videoUrl != null && videoUrl.isNotEmpty) {
      return {'videoUrl': videoUrl, 'apiData': null};
    }

    // If all methods fail, use a fallback video URL for demonstration
    debugPrint('All extraction methods failed. Using fallback URL.');
    final fallbackUrl = ApiConfig.getRandomFallbackVideoUrl();
    debugPrint('Using fallback video URL: $fallbackUrl');
    return {'videoUrl': fallbackUrl, 'apiData': null};
  }

  /// Make a request to the instagram-reels-downloader-api endpoint
  /// Returns both the video URL and additional metadata
  Future<Map<String, dynamic>?> _makeReelsDownloaderApiRequest(
      String endpoint, String instagramUrl) async {
    try {
      debugPrint('Making API request to reels downloader API: $endpoint');

      final headers = {
        'X-RapidAPI-Key': _rapidApiKey,
        'X-RapidAPI-Host': 'instagram-reels-downloader-api.p.rapidapi.com'
      };

      // Create a GET request with URL as a query parameter
      final uri =
          Uri.parse('$endpoint?url=${Uri.encodeComponent(instagramUrl)}');

      debugPrint('Making GET request to: $uri');
      final response = await http.get(uri, headers: headers);

      if (response.statusCode == 200) {
        debugPrint('Received successful response from API');
        final jsonResponse = jsonDecode(response.body);

        // Check if the response is successful
        if (jsonResponse['success'] == true && jsonResponse['data'] != null) {
          final data = jsonResponse['data'];
          String? videoUrl;

          // Extract video URL from the response based on the format you shared
          debugPrint('API response data: ${jsonEncode(data)}');

          // First, try to get the video URL directly from the data
          if (data['url'] != null &&
              data['url'].toString().contains('instagram') &&
              data['url'].toString().contains('.mp4')) {
            videoUrl = data['url'];
            debugPrint('Found direct video URL in data: $videoUrl');
          }
          // Then check the medias array
          else if (data['medias'] != null &&
              data['medias'] is List &&
              (data['medias'] as List).isNotEmpty) {
            // Find the video media with the highest quality
            var mediaList = data['medias'] as List;
            String? bestVideoUrl;
            int highestQuality = 0;

            // First pass: look for video type entries
            for (var media in mediaList) {
              debugPrint('Examining media: ${jsonEncode(media)}');

              if (media['type'] == 'video' && media['url'] != null) {
                String url = media['url'];
                // Check if quality information is available
                String quality = media['quality'] ?? '';
                int currentQuality = 0;

                // Parse quality like "640-1136p" to get the highest resolution
                if (quality.contains('-')) {
                  final parts = quality.split('-');
                  if (parts.length > 1 && parts[1].contains('p')) {
                    final resolution = parts[1].replaceAll('p', '');
                    try {
                      currentQuality = int.parse(resolution);
                    } catch (e) {
                      // If parsing fails, use default quality value
                    }
                  }
                }

                // If this is the first video or has higher quality than previous best
                if (bestVideoUrl == null || currentQuality > highestQuality) {
                  bestVideoUrl = url;
                  highestQuality = currentQuality;
                  debugPrint(
                      'Found video URL in medias: $bestVideoUrl (Quality: $quality)');
                }
              }
            }

            // Second pass: if no video type found, look for any URL that seems like a video
            if (bestVideoUrl == null) {
              for (var media in mediaList) {
                if (media['url'] != null) {
                  String url = media['url'];
                  if (url.contains('.mp4') || url.contains('video')) {
                    bestVideoUrl = url;
                    debugPrint('Found potential video URL: $bestVideoUrl');
                    break;
                  }
                }
              }
            }

            if (bestVideoUrl != null) {
              videoUrl = bestVideoUrl;
            }
          }

          // If no video found in medias, try other fields
          if (videoUrl == null && data['url'] != null) {
            videoUrl = data['url'];
          }

          if (videoUrl != null && videoUrl.isNotEmpty) {
            // Return both the video URL and the API data for additional metadata
            return {
              'videoUrl': videoUrl,
              'apiData': data,
            };
          }
        } else {
          debugPrint(
              'API response indicates failure: ${jsonResponse['message']}');
        }
      } else {
        debugPrint(
            'API request failed with status code: ${response.statusCode}');
        debugPrint('Response body: ${response.body}');
      }
    } catch (e) {
      debugPrint('Error making reels downloader API request: $e');
    }

    return null;
  }

  /// Fallback method to extract video URL without using an API
  /// This is used when the API key is not set or all API endpoints fail
  Future<String?> _extractWithoutApiKey(String instagramUrl) async {
    try {
      // Extract the shortcode from the URL
      String shortcode = '';
      if (instagramUrl.contains('/reel/')) {
        shortcode = instagramUrl.split('/reel/')[1].split('/')[0];
      } else if (instagramUrl.contains('/p/')) {
        shortcode = instagramUrl.split('/p/')[1].split('/')[0];
      }

      if (shortcode.isEmpty) {
        debugPrint('Could not extract shortcode from URL: $instagramUrl');
        return null;
      }

      // Try to fetch the Instagram page directly
      final response = await http.get(
        Uri.parse('https://www.instagram.com/reel/$shortcode/'),
        headers: {
          'User-Agent':
              'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml',
          'Accept-Language': 'en-US,en;q=0.9',
        },
      );

      if (response.statusCode != 200) {
        debugPrint('Failed to fetch Instagram page: ${response.statusCode}');
        return null;
      }

      final html = response.body;

      // Try to extract the video URL from the HTML
      // Pattern 1: Look for og:video content in meta tags (most reliable)
      final metaVideoRegExp =
          RegExp(r'<meta property="og:video" content="([^"]+)"');
      final metaVideoMatch = metaVideoRegExp.firstMatch(html);
      if (metaVideoMatch != null && metaVideoMatch.groupCount >= 1) {
        final videoUrl = metaVideoMatch.group(1);
        if (videoUrl != null && videoUrl.isNotEmpty) {
          debugPrint('Found video URL in meta tag: $videoUrl');
          return videoUrl;
        }
      }

      // Pattern 2: Look for video_url in JSON data
      final videoUrlRegExp = RegExp(r'"video_url":"([^"]*)"');
      final videoUrlMatch = videoUrlRegExp.firstMatch(html);
      if (videoUrlMatch != null && videoUrlMatch.groupCount >= 1) {
        final videoUrl = videoUrlMatch.group(1)?.replaceAll(r'\\/', '/');
        if (videoUrl != null && videoUrl.isNotEmpty) {
          debugPrint('Found video URL in JSON data: $videoUrl');
          return videoUrl;
        }
      }

      debugPrint('Could not extract video URL from Instagram page');
      return null;
    } catch (e) {
      debugPrint('Error in fallback extraction method: $e');
      return null;
    }
  }

  /// Make a request to a third-party API
  Future<String?> _makeApiRequest(String endpoint, String instagramUrl) async {
    try {
      debugPrint('Making API request to: $endpoint');

      // Different APIs have different request formats
      Map<String, String> headers;
      Uri uri;

      // Handle the instagram-reels-downloader-api endpoint (GET request with query parameters)
      if (endpoint.contains('instagram-reels-downloader-api')) {
        headers = {
          'X-RapidAPI-Key': _rapidApiKey,
          'X-RapidAPI-Host': 'instagram-reels-downloader-api.p.rapidapi.com'
        };

        // Create a GET request with URL as a query parameter
        uri = Uri.parse('$endpoint?url=${Uri.encodeComponent(instagramUrl)}');

        debugPrint('Making GET request to: $uri');
        final response = await http.get(uri, headers: headers);

        if (response.statusCode == 200) {
          debugPrint('Received successful response from API');
          final jsonResponse = jsonDecode(response.body);

          // Check if the response is successful
          if (jsonResponse['success'] == true && jsonResponse['data'] != null) {
            // Extract video URL from the response based on the format you shared
            if (jsonResponse['data']['medias'] != null &&
                jsonResponse['data']['medias'] is List &&
                (jsonResponse['data']['medias'] as List).isNotEmpty) {
              // Find the video media with the highest quality
              var mediaList = jsonResponse['data']['medias'] as List;
              String? bestVideoUrl;

              for (var media in mediaList) {
                if (media['type'] == 'video' && media['url'] != null) {
                  bestVideoUrl = media['url'];
                  debugPrint('Found video URL in medias: $bestVideoUrl');
                  break;
                }
              }

              if (bestVideoUrl != null) {
                return bestVideoUrl;
              }
            }

            // If no video found in medias, try other fields
            if (jsonResponse['data']['url'] != null) {
              return jsonResponse['data']['url'];
            }
          } else {
            debugPrint(
                'API response indicates failure: ${jsonResponse['message']}');
          }
        } else {
          debugPrint(
              'API request failed with status code: ${response.statusCode}');
          debugPrint('Response body: ${response.body}');
        }
      }
      // Handle other API endpoints (POST requests with JSON body)
      else {
        if (endpoint.contains(
            'instagram-downloader-download-instagram-videos-stories')) {
          headers = {
            'content-type': 'application/json',
            'X-RapidAPI-Key': _rapidApiKey,
            'X-RapidAPI-Host':
                'instagram-downloader-download-instagram-videos-stories.p.rapidapi.com'
          };
        } else if (endpoint.contains('instagram-media-downloader')) {
          headers = {
            'content-type': 'application/x-www-form-urlencoded',
            'X-RapidAPI-Key': _rapidApiKey,
            'X-RapidAPI-Host': 'instagram-media-downloader.p.rapidapi.com'
          };
        } else {
          headers = {
            'X-RapidAPI-Key': _rapidApiKey,
            'X-RapidAPI-Host': 'instagram-downloader-api.p.rapidapi.com'
          };
        }

        uri = Uri.parse(endpoint);
        final body = {'url': instagramUrl};

        debugPrint('Making POST request to: $uri');
        final response = await http.post(
          uri,
          headers: headers,
          body: jsonEncode(body),
        );

        if (response.statusCode == 200) {
          debugPrint('Received successful response from API');
          final jsonResponse = jsonDecode(response.body);

          // Different APIs have different response formats
          String? videoUrl;

          if (endpoint.contains(
              'instagram-downloader-download-instagram-videos-stories')) {
            videoUrl = jsonResponse['media'] ?? '';
          } else if (endpoint.contains('instagram-media-downloader')) {
            videoUrl = jsonResponse['video'] ?? '';
          } else {
            videoUrl = jsonResponse['data']['media'] ?? '';
          }

          if (videoUrl != null && videoUrl.isNotEmpty) {
            debugPrint('Extracted video URL: $videoUrl');
            return videoUrl;
          }
        } else {
          debugPrint(
              'API request failed with status code: ${response.statusCode}');
          debugPrint('Response body: ${response.body}');
        }
      }
    } catch (e) {
      debugPrint('Error making API request: $e');
    }

    return null;
  }

  /// Create a ReelModel from an Instagram URL and extracted video URL
  /// This method can also use additional information from the API response
  Future<ReelModel?> createReelModel(String instagramUrl, String videoUrl,
      {Map<String, dynamic>? apiData}) async {
    try {
      // Extract the shortcode from the URL
      String shortcode = '';
      if (instagramUrl.contains('/reel/')) {
        shortcode = instagramUrl.split('/reel/')[1].split('/')[0];
      } else if (instagramUrl.contains('/p/')) {
        shortcode = instagramUrl.split('/p/')[1].split('/')[0];
      }

      if (shortcode.isEmpty) {
        debugPrint('Could not extract shortcode from URL: $instagramUrl');
        return null;
      }

      // Default values
      String username = 'instagram_user';
      String caption = 'Instagram Reel';
      String thumbnailUrl = '';

      // If we have API data, use it
      if (apiData != null) {
        debugPrint('Using API data to create ReelModel');

        // Extract username from API data
        if (apiData['author'] != null) {
          username = apiData['author'];
        } else if (apiData['owner'] != null &&
            apiData['owner']['username'] != null) {
          username = apiData['owner']['username'];
        }

        // Extract caption/title from API data
        if (apiData['title'] != null && apiData['title'] != 'Instagram') {
          caption = apiData['title'];
        }

        // Extract thumbnail URL from API data
        if (apiData['thumbnail'] != null) {
          thumbnailUrl = apiData['thumbnail'];
        }

        debugPrint(
            'Extracted from API: username=$username, caption=$caption, thumbnailUrl=$thumbnailUrl');
      }
      // If no API data, try to fetch from Instagram directly
      else {
        debugPrint('No API data available, fetching from Instagram directly');
        try {
          final response = await http.get(
            Uri.parse('https://www.instagram.com/reel/$shortcode/'),
            headers: {
              'User-Agent':
                  'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
              'Accept': 'text/html,application/xhtml+xml,application/xml',
            },
          );

          if (response.statusCode == 200) {
            final html = response.body;

            // Extract username
            final usernameRegExp = RegExp(r'"username":\s*"([^"]+)"');
            final usernameMatch = usernameRegExp.firstMatch(html);
            if (usernameMatch != null && usernameMatch.groupCount >= 1) {
              username = usernameMatch.group(1) ?? username;
            }

            // Extract caption
            final captionRegExp = RegExp(r'"caption":\s*"([^"]*)"');
            final captionMatch = captionRegExp.firstMatch(html);
            if (captionMatch != null && captionMatch.groupCount >= 1) {
              caption = captionMatch.group(1) ?? caption;
              // Clean up escaped characters
              caption = caption.replaceAll(r'\n', '\n').replaceAll(r'\"', '"');
            }

            // Extract thumbnail URL
            final thumbnailRegExp = RegExp(r'"display_url":\s*"([^"]+)"');
            final thumbnailMatch = thumbnailRegExp.firstMatch(html);
            if (thumbnailMatch != null && thumbnailMatch.groupCount >= 1) {
              thumbnailUrl = thumbnailMatch.group(1) ?? '';
              // Clean up escaped characters
              thumbnailUrl = thumbnailUrl.replaceAll(r'\\/', '/');
            }

            // If thumbnail URL is still empty, try another pattern
            if (thumbnailUrl.isEmpty) {
              final ogImageRegExp =
                  RegExp(r'<meta property="og:image" content="([^"]+)"');
              final ogImageMatch = ogImageRegExp.firstMatch(html);
              if (ogImageMatch != null && ogImageMatch.groupCount >= 1) {
                thumbnailUrl = ogImageMatch.group(1) ?? '';
              }
            }
          }
        } catch (e) {
          debugPrint('Error fetching additional info from Instagram: $e');
          // Continue with default values
        }
      }

      // If thumbnail is still empty, use a placeholder
      if (thumbnailUrl.isEmpty) {
        thumbnailUrl = 'https://via.placeholder.com/500';
      }

      return ReelModel(
        id: shortcode,
        videoUrl: videoUrl,
        thumbnailUrl: thumbnailUrl,
        username: username,
        caption: caption,
        downloadPath: '',
        downloadDate: DateTime.now(),
      );
    } catch (e) {
      debugPrint('Error creating ReelModel: $e');
      return null;
    }
  }
}
