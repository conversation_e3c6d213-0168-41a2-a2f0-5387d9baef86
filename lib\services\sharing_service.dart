import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:instagram_reels_downloader/models/reel_model.dart';
import 'package:path/path.dart' as path;
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';

class SharingService {
  // Singleton instance
  static final SharingService _instance = SharingService._internal();
  
  factory SharingService() => _instance;
  
  SharingService._internal();
  
  // Share a reel
  Future<void> shareReel(ReelModel reel) async {
    try {
      if (kIsWeb) {
        // For web, just open the URL
        await _launchUrl(reel.videoUrl);
        return;
      }
      
      final file = File(reel.downloadPath);
      if (await file.exists()) {
        // Share the file
        await Share.shareXFiles(
          [XFile(reel.downloadPath)],
          text: 'Check out this Instagram Reel from @${reel.username}',
          subject: 'Instagram Reel',
        );
      } else {
        // File doesn't exist, share the URL instead
        await Share.share(
          'Check out this Instagram Reel from @${reel.username}: ${reel.videoUrl}',
          subject: 'Instagram Reel',
        );
      }
    } catch (e) {
      debugPrint('Error sharing reel: $e');
      // Fallback to sharing the URL
      await Share.share(
        'Check out this Instagram Reel from @${reel.username}: ${reel.videoUrl}',
        subject: 'Instagram Reel',
      );
    }
  }
  
  // Share a reel to a specific platform
  Future<void> shareReelToPlatform(ReelModel reel, SharingPlatform platform) async {
    try {
      if (kIsWeb) {
        // For web, just open the URL
        await _launchUrl(reel.videoUrl);
        return;
      }
      
      final file = File(reel.downloadPath);
      if (!await file.exists()) {
        debugPrint('File does not exist: ${reel.downloadPath}');
        return;
      }
      
      switch (platform) {
        case SharingPlatform.whatsapp:
          await _shareToWhatsApp(reel);
          break;
        case SharingPlatform.facebook:
          await _shareToFacebook(reel);
          break;
        case SharingPlatform.twitter:
          await _shareToTwitter(reel);
          break;
        case SharingPlatform.telegram:
          await _shareToTelegram(reel);
          break;
        case SharingPlatform.email:
          await _shareViaEmail(reel);
          break;
        case SharingPlatform.copyLink:
          await _copyVideoUrl(reel);
          break;
      }
    } catch (e) {
      debugPrint('Error sharing reel to ${platform.name}: $e');
    }
  }
  
  // Share to WhatsApp
  Future<void> _shareToWhatsApp(ReelModel reel) async {
    final whatsappUrl = Uri.parse('whatsapp://send?text=Check out this Instagram Reel from @${reel.username}');
    
    if (await canLaunchUrl(whatsappUrl)) {
      await Share.shareXFiles(
        [XFile(reel.downloadPath)],
        text: 'Check out this Instagram Reel from @${reel.username}',
      );
    } else {
      debugPrint('WhatsApp not installed');
    }
  }
  
  // Share to Facebook
  Future<void> _shareToFacebook(ReelModel reel) async {
    final facebookUrl = Uri.parse('https://www.facebook.com/sharer/sharer.php?u=${Uri.encodeComponent(reel.videoUrl)}');
    
    await _launchUrl(facebookUrl.toString());
  }
  
  // Share to Twitter
  Future<void> _shareToTwitter(ReelModel reel) async {
    final twitterUrl = Uri.parse('https://twitter.com/intent/tweet?text=Check out this Instagram Reel from @${reel.username}&url=${Uri.encodeComponent(reel.videoUrl)}');
    
    await _launchUrl(twitterUrl.toString());
  }
  
  // Share to Telegram
  Future<void> _shareToTelegram(ReelModel reel) async {
    final telegramUrl = Uri.parse('https://t.me/share/url?url=${Uri.encodeComponent(reel.videoUrl)}&text=Check out this Instagram Reel from @${reel.username}');
    
    await _launchUrl(telegramUrl.toString());
  }
  
  // Share via email
  Future<void> _shareViaEmail(ReelModel reel) async {
    final emailUrl = Uri.parse('mailto:?subject=Instagram Reel&body=Check out this Instagram Reel from @${reel.username}: ${reel.videoUrl}');
    
    await _launchUrl(emailUrl.toString());
  }
  
  // Copy video URL to clipboard
  Future<void> _copyVideoUrl(ReelModel reel) async {
    // This is handled in the UI
  }
  
  // Launch a URL
  Future<void> _launchUrl(String url) async {
    final uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      debugPrint('Could not launch $url');
    }
  }
}

// Sharing platforms
enum SharingPlatform {
  whatsapp,
  facebook,
  twitter,
  telegram,
  email,
  copyLink,
}

// Extension to get platform name
extension SharingPlatformExtension on SharingPlatform {
  String get name {
    switch (this) {
      case SharingPlatform.whatsapp:
        return 'WhatsApp';
      case SharingPlatform.facebook:
        return 'Facebook';
      case SharingPlatform.twitter:
        return 'Twitter';
      case SharingPlatform.telegram:
        return 'Telegram';
      case SharingPlatform.email:
        return 'Email';
      case SharingPlatform.copyLink:
        return 'Copy Link';
    }
  }
  
  String get iconPath {
    switch (this) {
      case SharingPlatform.whatsapp:
        return 'assets/icons/whatsapp.png';
      case SharingPlatform.facebook:
        return 'assets/icons/facebook.png';
      case SharingPlatform.twitter:
        return 'assets/icons/twitter.png';
      case SharingPlatform.telegram:
        return 'assets/icons/telegram.png';
      case SharingPlatform.email:
        return 'assets/icons/email.png';
      case SharingPlatform.copyLink:
        return 'assets/icons/copy.png';
    }
  }
}
