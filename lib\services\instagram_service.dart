import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:instagram_reels_downloader/models/reel_model.dart';
import 'package:instagram_reels_downloader/services/third_party_api_service.dart';

class InstagramService {
  // Third-party API service for fallback
  final ThirdPartyApiService _thirdPartyApiService = ThirdPartyApiService();
  // User agent to mimic a browser request
  static const String _userAgent =
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36';

  // Extract the shortcode from the Instagram URL
  String _extractShortcode(String url) {
    // Remove query parameters if any
    url = url.split('?').first;

    // Handle different URL formats
    if (url.contains('/reel/')) {
      return url.split('/reel/')[1].split('/')[0];
    } else if (url.contains('/p/')) {
      return url.split('/p/')[1].split('/')[0];
    } else if (url.contains('/tv/')) {
      return url.split('/tv/')[1].split('/')[0];
    }

    // If no pattern matches, return empty string
    return '';
  }

  // Validate if the URL is an Instagram URL
  bool isValidInstagramUrl(String url) {
    return url.contains('instagram.com') &&
        (url.contains('/reel/') || url.contains('/p/') || url.contains('/tv/'));
  }

  // Check if the URL is a direct video URL
  bool isDirectVideoUrl(String url) {
    // Direct video URLs typically end with .mp4 or similar extensions
    // and come from Instagram's CDN domains

    // Check for common video extensions
    final hasVideoExtension = url.contains('.mp4') ||
        url.contains('.mov') ||
        url.contains('.mpeg') ||
        url.contains('.m4v');

    // Check for Instagram/Facebook CDN domains
    final hasCdnDomain = url.contains('cdninstagram.com') ||
        url.contains('fbcdn.net') ||
        url.contains('fbcdn.com') ||
        url.contains('instagram.fvno') || // Common Instagram CDN pattern
        url.contains('scontent'); // Another common Instagram CDN pattern

    // Check for URL parameters that indicate a video resource
    final hasVideoParams =
        url.contains('efg=') || url.contains('_nc_ht=') || url.contains('/v/');

    // If it's an Instagram page URL, it's not a direct video URL
    final isInstagramPage =
        url.contains('instagram.com/reel/') || url.contains('instagram.com/p/');

    // Return true if it looks like a direct video URL and not an Instagram page
    return (hasVideoExtension || hasCdnDomain || hasVideoParams) &&
        !isInstagramPage;
  }

  // Fetch reel information from Instagram
  Future<ReelModel?> fetchReelInfo(String url) async {
    try {
      if (!isValidInstagramUrl(url)) {
        throw Exception('Invalid Instagram URL');
      }

      final shortcode = _extractShortcode(url);
      if (shortcode.isEmpty) {
        throw Exception('Could not extract shortcode from URL');
      }

      // Check if running on web
      if (kIsWeb) {
        // For web, we can't directly access Instagram due to CORS restrictions
        // In a real app, you would use a backend proxy server
        // For this demo, we'll create a mock response
        return _createMockReel(shortcode, url);
      }

      // Initialize variables for metadata
      String? videoUrl;
      String username = 'unknown';
      String caption = '';
      String thumbnailUrl = '';
      Map<String, dynamic>? apiData;

      // Skip direct extraction and go straight to the third-party API
      // This is more reliable than trying to parse Instagram's HTML
      debugPrint('Using third-party API to extract video URL...');

      // Try to get the video URL from the third-party API
      final apiResult = await _thirdPartyApiService.extractVideoUrl(url);
      final thirdPartyVideoUrl = apiResult['videoUrl'] as String?;
      apiData = apiResult['apiData'] as Map<String, dynamic>?;

      if (thirdPartyVideoUrl != null && thirdPartyVideoUrl.isNotEmpty) {
        videoUrl = thirdPartyVideoUrl;
        debugPrint(
            'Successfully extracted video URL using third-party API: $videoUrl');

        // If we have API data, use it to update metadata
        if (apiData != null) {
          debugPrint('Using API data for additional information');

          // Extract username from API data
          if (apiData['author'] != null) {
            username = apiData['author'] as String;
            debugPrint('Username from API: $username');
          } else if (apiData['owner'] != null &&
              apiData['owner']['username'] != null) {
            username = apiData['owner']['username'] as String;
            debugPrint('Username from API owner: $username');
          }

          // Extract caption/title from API data
          if (apiData['title'] != null && apiData['title'] != 'Instagram') {
            caption = apiData['title'] as String;
            debugPrint('Caption from API: $caption');
          }

          // Extract thumbnail URL from API data
          if (apiData['thumbnail'] != null) {
            thumbnailUrl = apiData['thumbnail'] as String;
            debugPrint('Thumbnail from API: $thumbnailUrl');
          }
        }
      } else {
        // If third-party API fails, try direct extraction as a fallback
        debugPrint('Third-party API failed. Trying direct extraction...');

        // For mobile platforms, we can try to access Instagram directly
        final response = await http.get(
          Uri.parse('https://www.instagram.com/reel/$shortcode/'),
          headers: {'User-Agent': _userAgent},
        );

        if (response.statusCode != 200) {
          throw Exception('Failed to load reel data: ${response.statusCode}');
        }

        // Extract the JSON data from the HTML
        final html = response.body;

        // Try multiple patterns for video URL extraction
        final videoUrlPatterns = [
          RegExp(r'"video_url":"([^"]*)"'),
          RegExp(r'"video_url"\s*:\s*"([^"]*)"'),
          RegExp(r'video_url=([^&"]+)'),
          RegExp(r'"playable_url":"([^"]+)"'),
          RegExp(r'"playable_url_quality_hd":"([^"]+)"'),
        ];

        for (final pattern in videoUrlPatterns) {
          final match = pattern.firstMatch(html);
          if (match != null && match.groupCount >= 1) {
            videoUrl = match.group(1);
            if (videoUrl != null && videoUrl.isNotEmpty) {
              debugPrint('Found video URL with pattern: ${pattern.pattern}');
              break;
            }
          }
        }

        // Pattern 2: Look for og:video content in meta tags
        if (videoUrl == null) {
          final metaVideoRegExp =
              RegExp(r'<meta property="og:video" content="([^"]+)"');
          final metaVideoMatch = metaVideoRegExp.firstMatch(html);
          if (metaVideoMatch != null && metaVideoMatch.groupCount >= 1) {
            videoUrl = metaVideoMatch.group(1);
            debugPrint('Found video URL in meta tag');
          }
        }

        if (videoUrl != null) {
          // Clean up the URL (replace escaped characters)
          videoUrl = videoUrl.replaceAll(r'\u0026', '&');
          videoUrl = videoUrl.replaceAll(r'\\u0026', '&');
          videoUrl = videoUrl.replaceAll(r'\\/', '/');

          debugPrint('Extracted video URL directly: $videoUrl');

          // Extract metadata from HTML
          // Extract username with multiple patterns
          final usernamePatterns = [
            RegExp(r'"username":\s*"([^"]*)"'),
            RegExp(r'"username":\s*"([^"]+)"'),
            RegExp(r'@([a-zA-Z0-9._]+)'),
          ];

          for (var pattern in usernamePatterns) {
            final match = pattern.firstMatch(html);
            if (match != null && match.groupCount >= 1) {
              username = match.group(1) ?? username;
              break;
            }
          }

          // Extract caption with multiple patterns
          final captionPatterns = [
            RegExp(r'"caption":\s*"([^"]*)"'),
            RegExp(r'"caption":\s*"([^"]+)"'),
            RegExp(r'"accessibility_caption":\s*"([^"]+)"'),
          ];

          for (var pattern in captionPatterns) {
            final match = pattern.firstMatch(html);
            if (match != null && match.groupCount >= 1) {
              caption = match.group(1) ?? caption;
              break;
            }
          }

          // Extract thumbnail with multiple patterns
          final thumbnailPatterns = [
            RegExp(r'"display_url":\s*"([^"]+)"'),
            RegExp(r'"thumbnail_src":\s*"([^"]+)"'),
            RegExp(r'"og:image"\s+content="([^"]+)"'),
          ];

          for (var pattern in thumbnailPatterns) {
            final match = pattern.firstMatch(html);
            if (match != null && match.groupCount >= 1) {
              thumbnailUrl = match.group(1) ?? thumbnailUrl;
              break;
            }
          }

          // Clean up the thumbnail URL
          if (thumbnailUrl.isNotEmpty) {
            thumbnailUrl = thumbnailUrl.replaceAll(r'\u0026', '&');
          }
        } else {
          // If all methods fail, use a fallback URL
          videoUrl = "https://www.instagram.com/reel/$shortcode/";
          debugPrint('All extraction methods failed. Using fallback URL.');
        }
      }

      // Make sure videoUrl is not null (required by ReelModel)
      videoUrl ??= "https://www.instagram.com/reel/$shortcode/";

      // If thumbnail is still empty, use a placeholder
      if (thumbnailUrl.isEmpty) {
        thumbnailUrl = 'https://via.placeholder.com/500';
      }

      return ReelModel(
        id: shortcode,
        videoUrl: videoUrl,
        thumbnailUrl: thumbnailUrl,
        username: username,
        caption: caption,
        downloadPath: '',
        downloadDate: DateTime.now(),
      );
    } catch (e) {
      debugPrint('Error fetching reel info: $e');
      return null;
    }
  }

  // Create a mock reel for web demo
  ReelModel _createMockReel(String shortcode, String originalUrl) {
    return ReelModel(
      id: shortcode,
      videoUrl: 'https://example.com/mock_video.mp4', // Mock URL
      thumbnailUrl: 'https://via.placeholder.com/500', // Placeholder image
      username: 'instagram_user',
      caption:
          'This is a mock reel for demonstration purposes. In a real app, you would need a backend server to proxy requests to Instagram.',
      downloadPath: '',
      downloadDate: DateTime.now(),
    );
  }
}
