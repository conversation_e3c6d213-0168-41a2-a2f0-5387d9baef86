import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:instagram_reels_downloader/models/reel_model.dart';
import 'package:instagram_reels_downloader/screens/video_preview_screen.dart';
import 'package:instagram_reels_downloader/utils/app_theme.dart';
import 'package:shimmer/shimmer.dart';

class ReelPreview extends StatelessWidget {
  final ReelModel reel;

  const ReelPreview({Key? key, required this.reel}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Reel thumbnail with play button
              Stack(
                alignment: Alignment.center,
                children: [
                  // Thumbnail with cached network image
                  if (reel.thumbnailUrl.isNotEmpty)
                    ClipRRect(
                      borderRadius: AppTheme.defaultBorderRadius,
                      child: CachedNetworkImage(
                        imageUrl: reel.thumbnailUrl,
                        height: 200,
                        width: double.infinity,
                        fit: BoxFit.cover,
                        placeholder: (context, url) => Shimmer.fromColors(
                          baseColor: Colors.grey.shade300,
                          highlightColor: Colors.grey.shade100,
                          child: Container(
                            height: 200,
                            width: double.infinity,
                            color: Colors.grey.shade300,
                          ),
                        ),
                        errorWidget: (context, url, error) => Container(
                          height: 200,
                          width: double.infinity,
                          decoration: BoxDecoration(
                            color: Colors.grey.shade300,
                            borderRadius: AppTheme.defaultBorderRadius,
                          ),
                          child: Center(
                            child: Icon(
                              Icons.image_not_supported,
                              size: 48,
                              color: Colors.grey.shade700,
                            ),
                          ),
                        ),
                      ),
                    )
                  else
                    Container(
                      height: 200,
                      width: double.infinity,
                      color: Colors.grey.shade300,
                      child: Center(
                        child: Icon(
                          Icons.video_file,
                          size: 48,
                          color: Colors.grey.shade700,
                        ),
                      ),
                    ),

                  // Play button overlay
                  Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) =>
                                VideoPreviewScreen(reel: reel),
                          ),
                        );
                      },
                      borderRadius: BorderRadius.circular(8),
                      child: Ink(
                        height: 200,
                        width: double.infinity,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          color: Colors.black.withOpacity(0.3),
                        ),
                        child: Center(
                          child: Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: Colors.black.withOpacity(0.5),
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.play_arrow,
                              color: Colors.white,
                              size: 48,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // User info row
              Row(
                children: [
                  // Profile picture placeholder
                  CircleAvatar(
                    radius: 20,
                    backgroundColor:
                        Theme.of(context).primaryColor.withOpacity(0.1),
                    child: reel.profilePicUrl?.isNotEmpty == true
                        ? CachedNetworkImage(
                            imageUrl: reel.profilePicUrl!,
                            imageBuilder: (context, imageProvider) => Container(
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                image: DecorationImage(
                                  image: imageProvider,
                                  fit: BoxFit.cover,
                                ),
                              ),
                            ),
                            placeholder: (context, url) =>
                                const CircularProgressIndicator(),
                            errorWidget: (context, url, error) => Icon(
                              Icons.person,
                              color: Theme.of(context).primaryColor,
                            ),
                          )
                        : Icon(
                            Icons.person,
                            color: Theme.of(context).primaryColor,
                          ),
                  ),
                  const SizedBox(width: 12),

                  // Username and verification
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Text(
                              '@${reel.username}',
                              style: Theme.of(context)
                                  .textTheme
                                  .titleMedium
                                  ?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                            ),
                            if (reel.isVerified) ...[
                              const SizedBox(width: 4),
                              Icon(
                                Icons.verified,
                                size: 16,
                                color: Theme.of(context).primaryColor,
                              ),
                            ],
                          ],
                        ),
                        if (reel.originalPostDate != null)
                          Text(
                            _getRelativeTime(reel.originalPostDate!),
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Video metadata row
              if (reel.videoDuration != null ||
                  reel.videoQuality != null ||
                  reel.fileSize != null)
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surface,
                    borderRadius: AppTheme.smallBorderRadius,
                    border: Border.all(
                      color: Theme.of(context).dividerColor,
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      if (reel.videoDuration != null)
                        _buildMetadataItem(
                          context,
                          Icons.access_time,
                          'Duration',
                          reel.formattedDuration,
                        ),
                      if (reel.videoQuality != null)
                        _buildMetadataItem(
                          context,
                          Icons.high_quality,
                          'Quality',
                          reel.videoQuality!,
                        ),
                      if (reel.fileSize != null)
                        _buildMetadataItem(
                          context,
                          Icons.storage,
                          'Size',
                          reel.formattedFileSize,
                        ),
                    ],
                  ),
                ),

              if (reel.videoDuration != null ||
                  reel.videoQuality != null ||
                  reel.fileSize != null)
                const SizedBox(height: 16),

              // Caption
              if (reel.caption.isNotEmpty) ...[
                Text(
                  'Caption',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const SizedBox(height: 8),
                Container(
                  constraints: const BoxConstraints(maxHeight: 120),
                  child: SingleChildScrollView(
                    child: Text(
                      reel.caption,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ),
                ),
                const SizedBox(height: 16),
              ],

              // Hashtags
              if (reel.hashtags.isNotEmpty) ...[
                Text(
                  'Hashtags',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  runSpacing: 4,
                  children: reel.hashtags
                      .map((hashtag) => Chip(
                            label: Text(
                              hashtag,
                              style: const TextStyle(fontSize: 12),
                            ),
                            backgroundColor:
                                Theme.of(context).primaryColor.withOpacity(0.1),
                            side: BorderSide.none,
                            materialTapTargetSize:
                                MaterialTapTargetSize.shrinkWrap,
                          ))
                      .toList(),
                ),
                const SizedBox(height: 16),
              ],

              // Music info
              if (reel.musicTitle?.isNotEmpty == true ||
                  reel.musicArtist?.isNotEmpty == true) ...[
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor.withOpacity(0.05),
                    borderRadius: AppTheme.smallBorderRadius,
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.music_note,
                        color: Theme.of(context).primaryColor,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            if (reel.musicTitle?.isNotEmpty == true)
                              Text(
                                reel.musicTitle!,
                                style: Theme.of(context)
                                    .textTheme
                                    .bodyMedium
                                    ?.copyWith(
                                      fontWeight: FontWeight.w600,
                                    ),
                              ),
                            if (reel.musicArtist?.isNotEmpty == true)
                              Text(
                                'by ${reel.musicArtist}',
                                style: Theme.of(context).textTheme.bodySmall,
                              ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMetadataItem(
    BuildContext context,
    IconData icon,
    String label,
    String value,
  ) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: 16,
          color: Theme.of(context).primaryColor,
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontSize: 10,
                fontWeight: FontWeight.w500,
              ),
        ),
        Text(
          value,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
      ],
    );
  }

  String _getRelativeTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays == 1 ? '' : 's'} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours == 1 ? '' : 's'} ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minute${difference.inMinutes == 1 ? '' : 's'} ago';
    } else {
      return 'Just now';
    }
  }
}
