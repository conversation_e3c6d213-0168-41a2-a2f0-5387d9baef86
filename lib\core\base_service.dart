import 'package:flutter/foundation.dart';
import 'package:instagram_reels_downloader/core/constants.dart';

/// Base service class that provides common functionality for all services
abstract class BaseService {
  /// Service name for logging
  String get serviceName;
  
  /// Initialize the service
  Future<void> initialize() async {
    debugPrint('[$serviceName] Initializing service...');
  }
  
  /// Dispose the service
  Future<void> dispose() async {
    debugPrint('[$serviceName] Disposing service...');
  }
  
  /// Log debug message
  void logDebug(String message) {
    debugPrint('[$serviceName] $message');
  }
  
  /// Log info message
  void logInfo(String message) {
    debugPrint('[$serviceName] INFO: $message');
  }
  
  /// Log warning message
  void logWarning(String message) {
    debugPrint('[$serviceName] WARNING: $message');
  }
  
  /// Log error message
  void logError(String message, [Object? error, StackTrace? stackTrace]) {
    debugPrint('[$serviceName] ERROR: $message');
    if (error != null) {
      debugPrint('[$serviceName] Error details: $error');
    }
    if (stackTrace != null) {
      debugPrint('[$serviceName] Stack trace: $stackTrace');
    }
  }
  
  /// Handle service errors with retry logic
  Future<T?> handleServiceCall<T>(
    Future<T> Function() serviceCall, {
    int maxRetries = AppConstants.maxRetryAttempts,
    Duration retryDelay = AppConstants.retryDelay,
    String? operationName,
  }) async {
    int attempts = 0;
    
    while (attempts < maxRetries) {
      try {
        attempts++;
        logDebug('${operationName ?? 'Operation'} attempt $attempts/$maxRetries');
        
        final result = await serviceCall();
        
        if (attempts > 1) {
          logInfo('${operationName ?? 'Operation'} succeeded on attempt $attempts');
        }
        
        return result;
      } catch (error, stackTrace) {
        logError(
          '${operationName ?? 'Operation'} failed on attempt $attempts: $error',
          error,
          stackTrace,
        );
        
        if (attempts >= maxRetries) {
          logError('${operationName ?? 'Operation'} failed after $maxRetries attempts');
          rethrow;
        }
        
        if (attempts < maxRetries) {
          logDebug('Retrying in ${retryDelay.inSeconds} seconds...');
          await Future.delayed(retryDelay);
        }
      }
    }
    
    return null;
  }
  
  /// Validate required parameters
  void validateRequired(Map<String, dynamic> parameters) {
    for (final entry in parameters.entries) {
      if (entry.value == null) {
        throw ArgumentError('Required parameter "${entry.key}" is null');
      }
      
      if (entry.value is String && (entry.value as String).isEmpty) {
        throw ArgumentError('Required parameter "${entry.key}" is empty');
      }
    }
  }
  
  /// Check if service is available (network, permissions, etc.)
  Future<bool> isServiceAvailable() async {
    return true; // Override in subclasses
  }
  
  /// Get service status
  Future<ServiceStatus> getServiceStatus() async {
    try {
      final isAvailable = await isServiceAvailable();
      return ServiceStatus(
        isAvailable: isAvailable,
        message: isAvailable ? 'Service is available' : 'Service is not available',
        lastChecked: DateTime.now(),
      );
    } catch (error) {
      return ServiceStatus(
        isAvailable: false,
        message: 'Error checking service status: $error',
        lastChecked: DateTime.now(),
        error: error,
      );
    }
  }
}

/// Service status information
class ServiceStatus {
  final bool isAvailable;
  final String message;
  final DateTime lastChecked;
  final Object? error;
  
  const ServiceStatus({
    required this.isAvailable,
    required this.message,
    required this.lastChecked,
    this.error,
  });
  
  @override
  String toString() {
    return 'ServiceStatus(isAvailable: $isAvailable, message: $message, lastChecked: $lastChecked)';
  }
}

/// Service result wrapper
class ServiceResult<T> {
  final bool isSuccess;
  final T? data;
  final String? errorMessage;
  final Object? error;
  final StackTrace? stackTrace;
  
  const ServiceResult._({
    required this.isSuccess,
    this.data,
    this.errorMessage,
    this.error,
    this.stackTrace,
  });
  
  /// Create a successful result
  factory ServiceResult.success(T data) {
    return ServiceResult._(
      isSuccess: true,
      data: data,
    );
  }
  
  /// Create a failed result
  factory ServiceResult.failure(
    String errorMessage, {
    Object? error,
    StackTrace? stackTrace,
  }) {
    return ServiceResult._(
      isSuccess: false,
      errorMessage: errorMessage,
      error: error,
      stackTrace: stackTrace,
    );
  }
  
  /// Check if result is successful
  bool get isFailure => !isSuccess;
  
  /// Get data or throw if failed
  T get dataOrThrow {
    if (isSuccess && data != null) {
      return data!;
    }
    throw Exception(errorMessage ?? 'Service call failed');
  }
  
  /// Get data or return default value
  T getDataOrDefault(T defaultValue) {
    return isSuccess && data != null ? data! : defaultValue;
  }
  
  @override
  String toString() {
    if (isSuccess) {
      return 'ServiceResult.success($data)';
    } else {
      return 'ServiceResult.failure($errorMessage)';
    }
  }
}

/// Service exception
class ServiceException implements Exception {
  final String message;
  final String serviceName;
  final Object? originalError;
  final StackTrace? stackTrace;
  
  const ServiceException(
    this.message,
    this.serviceName, {
    this.originalError,
    this.stackTrace,
  });
  
  @override
  String toString() {
    return 'ServiceException in $serviceName: $message';
  }
}
