import 'package:flutter/material.dart';
import 'package:instagram_reels_downloader/utils/app_theme.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ThemeProvider extends ChangeNotifier {
  // Theme mode (light, dark, system)
  ThemeMode _themeMode = ThemeMode.system;

  // Primary color for the app
  Color _primaryColor = Colors.pink;

  // Keys for shared preferences
  static const String _themeModeKey = 'theme_mode';
  static const String _primaryColorKey = 'primary_color';

  // Getters
  ThemeMode get themeMode => _themeMode;
  Color get primaryColor => _primaryColor;

  // Constructor - load saved preferences
  ThemeProvider() {
    _loadPreferences();
  }

  // Load saved preferences
  Future<void> _loadPreferences() async {
    final prefs = await SharedPreferences.getInstance();

    // Load theme mode
    final themeModeString = prefs.getString(_themeModeKey);
    if (themeModeString != null) {
      _themeMode = _getThemeModeFromString(themeModeString);
    }

    // Load primary color
    final primaryColorValue = prefs.getInt(_primaryColorKey);
    if (primaryColorValue != null) {
      _primaryColor = Color(primaryColorValue);
    }

    notifyListeners();
  }

  // Save preferences
  Future<void> _savePreferences() async {
    final prefs = await SharedPreferences.getInstance();

    // Save theme mode
    await prefs.setString(_themeModeKey, _themeMode.toString());

    // Save primary color
    await prefs.setInt(_primaryColorKey, _primaryColor.value);
  }

  // Set theme mode
  Future<void> setThemeMode(ThemeMode mode) async {
    _themeMode = mode;
    await _savePreferences();
    notifyListeners();
  }

  // Set primary color
  Future<void> setPrimaryColor(Color color) async {
    _primaryColor = color;
    await _savePreferences();
    notifyListeners();
  }

  // Toggle between light and dark mode
  Future<void> toggleThemeMode() async {
    if (_themeMode == ThemeMode.light) {
      await setThemeMode(ThemeMode.dark);
    } else {
      await setThemeMode(ThemeMode.light);
    }
  }

  // Get theme mode from string
  ThemeMode _getThemeModeFromString(String themeModeString) {
    switch (themeModeString) {
      case 'ThemeMode.light':
        return ThemeMode.light;
      case 'ThemeMode.dark':
        return ThemeMode.dark;
      case 'ThemeMode.system':
        return ThemeMode.system;
      default:
        return ThemeMode.system;
    }
  }

  // Get light theme data
  ThemeData getLightTheme() {
    return AppTheme.getLightTheme();
  }

  // Get dark theme data
  ThemeData getDarkTheme() {
    return AppTheme.getDarkTheme();
  }
}
