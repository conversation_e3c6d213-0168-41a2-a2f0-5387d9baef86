# Instagram Reels Downloader

A Flutter application that allows users to download and manage Instagram Reels videos. This app provides a clean and intuitive interface for downloading, viewing, and sharing Instagram Reels.

## Features

-   **Download Instagram Reels**: Easily download reels by pasting the URL
-   **Video Preview**: Preview reels before downloading
-   **Download Queue**: Manage multiple downloads with a queue system
-   **Full-Screen Video Player**: Enjoy videos in full-screen mode
-   **Swipe Navigation**: Switch between downloaded videos by swiping left or right
-   **Download Metrics**: View file size, download speed, and time remaining
-   **Share Options**: Share downloaded videos across various platforms
-   **Dark Mode Support**: Comfortable viewing in any lighting condition

## Screenshots

_Screenshots will be added here_

## Getting Started

### Prerequisites

-   Flutter SDK (2.0.0 or higher)
-   Dart SDK (2.12.0 or higher)
-   Android Studio / VS Code
-   Android SDK (for Android deployment)
-   Xcode (for iOS deployment)

### Installation

1. Clone this repository:

```bash
git clone https://github.com/yourusername/instagram_reels_downloader.git
```

2. Navigate to the project directory:

```bash
cd instagram_reels_downloader
```

3. Install dependencies:

```bash
flutter pub get
```

4. Run the app:

```bash
flutter run
```

### Building for Production

To build a release version of the app:

```bash
flutter build apk --release  # For Android
flutter build ios --release  # For iOS
```

## Architecture

The app follows a clean architecture approach with Provider for state management:

-   **Models**: Data structures representing the core entities
-   **Providers**: State management using the Provider package
-   **Services**: Business logic and API interactions
-   **Screens**: UI components and user interactions
-   **Widgets**: Reusable UI components
-   **Utils**: Helper functions and utilities

## API Integration

The app uses RapidAPI's Instagram Reels Downloader API for fetching video information. To use your own API key:

1. Sign up at [RapidAPI](https://rapidapi.com/)
2. Subscribe to the [Instagram Reels Downloader API](https://rapidapi.com/search/instagram)
3. Replace the API key in `lib/services/third_party_api_service.dart`

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

-   [Flutter](https://flutter.dev/) - UI toolkit for building natively compiled applications
-   [Provider](https://pub.dev/packages/provider) - State management solution
-   [Chewie](https://pub.dev/packages/chewie) - Video player for Flutter
-   [Video Player](https://pub.dev/packages/video_player) - Flutter plugin for playing videos
-   [Share Plus](https://pub.dev/packages/share_plus) - Sharing content via the platform's share dialog
-   [Path Provider](https://pub.dev/packages/path_provider) - Finding commonly used locations on the filesystem
-   [HTTP](https://pub.dev/packages/http) - Making HTTP requests

## Disclaimer

This app is for educational purposes only. Please respect Instagram's terms of service and copyright laws when using this application. Always ensure you have the right to download and share content.
