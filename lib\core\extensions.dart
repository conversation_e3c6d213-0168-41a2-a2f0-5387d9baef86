import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

/// Extension methods for common operations

/// String extensions
extension StringExtensions on String {
  /// Check if string is a valid Instagram URL
  bool get isValidInstagramUrl {
    final patterns = [
      'instagram.com/reel/',
      'instagram.com/p/',
      'instagram.com/tv/',
      'instagr.am/reel/',
      'instagr.am/p/',
    ];
    
    return patterns.any((pattern) => toLowerCase().contains(pattern));
  }
  
  /// Extract Instagram shortcode from URL
  String get instagramShortcode {
    final regex = RegExp(r'(?:reel|p|tv)\/([A-Za-z0-9_-]+)');
    final match = regex.firstMatch(this);
    return match?.group(1) ?? '';
  }
  
  /// Sanitize filename by removing invalid characters
  String get sanitizedFilename {
    return replaceAll(RegExp(r'[<>:"/\\|?*]'), '_')
        .replaceAll(RegExp(r'\s+'), '_')
        .trim();
  }
  
  /// Capitalize first letter
  String get capitalized {
    if (isEmpty) return this;
    return '${this[0].toUpperCase()}${substring(1)}';
  }
  
  /// Check if string is empty or null
  bool get isNullOrEmpty => isEmpty;
  
  /// Check if string is not empty and not null
  bool get isNotNullOrEmpty => isNotEmpty;
  
  /// Convert string to title case
  String get titleCase {
    return split(' ')
        .map((word) => word.capitalized)
        .join(' ');
  }
}

/// DateTime extensions
extension DateTimeExtensions on DateTime {
  /// Format date for filename (YYYY-MM-DD_HH-mm-ss)
  String get filenameFormat {
    return DateFormat('yyyy-MM-dd_HH-mm-ss').format(this);
  }
  
  /// Format date for display (MMM dd, yyyy)
  String get displayFormat {
    return DateFormat('MMM dd, yyyy').format(this);
  }
  
  /// Format date and time for display (MMM dd, yyyy at HH:mm)
  String get displayFormatWithTime {
    return DateFormat('MMM dd, yyyy \'at\' HH:mm').format(this);
  }
  
  /// Get relative time (e.g., "2 hours ago")
  String get relativeTime {
    final now = DateTime.now();
    final difference = now.difference(this);
    
    if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays == 1 ? '' : 's'} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours == 1 ? '' : 's'} ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minute${difference.inMinutes == 1 ? '' : 's'} ago';
    } else {
      return 'Just now';
    }
  }
  
  /// Check if date is today
  bool get isToday {
    final now = DateTime.now();
    return year == now.year && month == now.month && day == now.day;
  }
  
  /// Check if date is yesterday
  bool get isYesterday {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return year == yesterday.year && month == yesterday.month && day == yesterday.day;
  }
}

/// Int extensions for file sizes
extension IntExtensions on int {
  /// Format bytes to human readable format
  String get formatBytes {
    if (this < 1024) return '$this B';
    if (this < 1024 * 1024) return '${(this / 1024).toStringAsFixed(1)} KB';
    if (this < 1024 * 1024 * 1024) return '${(this / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(this / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
  
  /// Format duration in seconds to human readable format
  String get formatDuration {
    final duration = Duration(seconds: this);
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);
    
    if (hours > 0) {
      return '${hours}h ${minutes}m ${seconds}s';
    } else if (minutes > 0) {
      return '${minutes}m ${seconds}s';
    } else {
      return '${seconds}s';
    }
  }
}

/// Double extensions
extension DoubleExtensions on double {
  /// Format percentage
  String get formatPercentage {
    return '${(this * 100).toStringAsFixed(0)}%';
  }
  
  /// Format speed (bytes per second)
  String get formatSpeed {
    final bytesPerSecond = toInt();
    if (bytesPerSecond < 1024) return '$bytesPerSecond B/s';
    if (bytesPerSecond < 1024 * 1024) return '${(bytesPerSecond / 1024).toStringAsFixed(1)} KB/s';
    return '${(bytesPerSecond / (1024 * 1024)).toStringAsFixed(1)} MB/s';
  }
}

/// BuildContext extensions
extension BuildContextExtensions on BuildContext {
  /// Get theme data
  ThemeData get theme => Theme.of(this);
  
  /// Get color scheme
  ColorScheme get colorScheme => theme.colorScheme;
  
  /// Get text theme
  TextTheme get textTheme => theme.textTheme;
  
  /// Get media query
  MediaQueryData get mediaQuery => MediaQuery.of(this);
  
  /// Get screen size
  Size get screenSize => mediaQuery.size;
  
  /// Get screen width
  double get screenWidth => screenSize.width;
  
  /// Get screen height
  double get screenHeight => screenSize.height;
  
  /// Check if device is in dark mode
  bool get isDarkMode => theme.brightness == Brightness.dark;
  
  /// Show snackbar with message
  void showSnackBar(String message, {Color? backgroundColor, Duration? duration}) {
    ScaffoldMessenger.of(this).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: backgroundColor,
        duration: duration ?? const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
  
  /// Show error snackbar
  void showErrorSnackBar(String message) {
    showSnackBar(message, backgroundColor: colorScheme.error);
  }
  
  /// Show success snackbar
  void showSuccessSnackBar(String message) {
    showSnackBar(message, backgroundColor: Colors.green);
  }
  
  /// Navigate to page
  Future<T?> navigateTo<T>(Widget page) {
    return Navigator.of(this).push<T>(
      MaterialPageRoute(builder: (_) => page),
    );
  }
  
  /// Navigate and replace current page
  Future<T?> navigateAndReplace<T>(Widget page) {
    return Navigator.of(this).pushReplacement<T, void>(
      MaterialPageRoute(builder: (_) => page),
    );
  }
  
  /// Pop current page
  void pop<T>([T? result]) {
    Navigator.of(this).pop(result);
  }
}

/// List extensions
extension ListExtensions<T> on List<T> {
  /// Get element at index or null if out of bounds
  T? elementAtOrNull(int index) {
    if (index >= 0 && index < length) {
      return this[index];
    }
    return null;
  }
  
  /// Check if list is null or empty
  bool get isNullOrEmpty => isEmpty;
  
  /// Check if list is not null and not empty
  bool get isNotNullOrEmpty => isNotEmpty;
}
