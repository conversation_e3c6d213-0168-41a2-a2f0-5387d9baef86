import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:instagram_reels_downloader/models/reel_model.dart';
import 'package:permission_handler/permission_handler.dart';

class NotificationService {
  // Singleton instance
  static final NotificationService _instance = NotificationService._internal();
  
  factory NotificationService() => _instance;
  
  NotificationService._internal();
  
  // Notification plugin
  final FlutterLocalNotificationsPlugin _notificationsPlugin = 
      FlutterLocalNotificationsPlugin();
  
  // Notification IDs
  static const int _downloadProgressNotificationId = 1;
  static const int _downloadCompletedNotificationId = 2;
  
  // Initialize notifications
  Future<void> initialize() async {
    // Skip for web platform
    if (kIsWeb) {
      return;
    }
    
    // Request notification permission
    await _requestPermissions();
    
    // Initialize settings
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');
    
    const DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );
    
    const InitializationSettings initializationSettings = InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIOS,
    );
    
    await _notificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );
  }
  
  // Request notification permissions
  Future<void> _requestPermissions() async {
    if (kIsWeb) {
      return;
    }
    
    // For Android
    if (defaultTargetPlatform == TargetPlatform.android) {
      await Permission.notification.request();
    }
  }
  
  // Handle notification tap
  void _onNotificationTapped(NotificationResponse response) {
    // Handle notification tap
    debugPrint('Notification tapped: ${response.payload}');
    
    // TODO: Navigate to the appropriate screen based on the payload
  }
  
  // Show download progress notification
  Future<void> showDownloadProgressNotification({
    required String title,
    required int progress,
    required int total,
    String? username,
  }) async {
    if (kIsWeb) {
      return;
    }
    
    // Calculate progress percentage
    final percentage = total > 0 ? (progress / total * 100).round() : 0;
    
    // Create Android notification details
    final AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
      'download_channel',
      'Downloads',
      channelDescription: 'Notifications for download progress',
      importance: Importance.low,
      priority: Priority.low,
      showProgress: true,
      maxProgress: 100,
      progress: percentage,
      ongoing: true,
      autoCancel: false,
    );
    
    // Create iOS notification details
    const DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
      presentAlert: false,
      presentBadge: false,
      presentSound: false,
    );
    
    // Create notification details
    final NotificationDetails notificationDetails = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );
    
    // Show notification
    await _notificationsPlugin.show(
      _downloadProgressNotificationId,
      title,
      'Downloading ${username != null ? "from @$username" : ""} - $percentage%',
      notificationDetails,
    );
  }
  
  // Show download completed notification
  Future<void> showDownloadCompletedNotification({
    required ReelModel reel,
    required bool success,
    String? errorMessage,
  }) async {
    if (kIsWeb) {
      return;
    }
    
    // Cancel progress notification
    await _notificationsPlugin.cancel(_downloadProgressNotificationId);
    
    // Create notification details
    final AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
      'download_channel',
      'Downloads',
      channelDescription: 'Notifications for download progress',
      importance: Importance.high,
      priority: Priority.high,
      autoCancel: true,
    );
    
    const DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );
    
    final NotificationDetails notificationDetails = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );
    
    // Show notification
    if (success) {
      await _notificationsPlugin.show(
        _downloadCompletedNotificationId,
        'Download Completed',
        'Reel from @${reel.username} has been downloaded successfully',
        notificationDetails,
        payload: reel.id,
      );
    } else {
      await _notificationsPlugin.show(
        _downloadCompletedNotificationId,
        'Download Failed',
        'Failed to download reel from @${reel.username}${errorMessage != null ? ": $errorMessage" : ""}',
        notificationDetails,
      );
    }
  }
  
  // Cancel all notifications
  Future<void> cancelAllNotifications() async {
    if (kIsWeb) {
      return;
    }
    
    await _notificationsPlugin.cancelAll();
  }
}
