import 'dart:async';
import 'dart:io';

import 'package:chewie/chewie.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:instagram_reels_downloader/models/reel_model.dart';
import 'package:video_player/video_player.dart';

class VideoPlayerScreen extends StatefulWidget {
  final List<ReelModel> reels;
  final int initialIndex;

  const VideoPlayerScreen({
    Key? key,
    required this.reels,
    required this.initialIndex,
  }) : super(key: key);

  @override
  State<VideoPlayerScreen> createState() => _VideoPlayerScreenState();
}

class _VideoPlayerScreenState extends State<VideoPlayerScreen> {
  late PageController _pageController;
  late int _currentIndex;
  final Map<int, VideoPlayerController> _videoControllers = {};
  final Map<int, ChewieController> _chewieControllers = {};
  final Map<int, bool> _isInitialized = {};
  final Map<int, String> _errorMessages = {};

  // UI control variables
  bool _showControls = true;
  double _volume = 1.0;
  double _brightness = 0.5;
  Timer? _controlsTimer;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: _currentIndex);

    // Set preferred orientations to landscape and portrait
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);

    // Enter full screen mode
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);

    // Initialize the current video and preload adjacent videos
    _initializeVideoPlayer(_currentIndex);
    if (_currentIndex > 0) {
      _initializeVideoPlayer(_currentIndex - 1);
    }
    if (_currentIndex < widget.reels.length - 1) {
      _initializeVideoPlayer(_currentIndex + 1);
    }

    // Start the controls auto-hide timer
    _startControlsTimer();
  }

  @override
  void dispose() {
    // Dispose all controllers
    for (final controller in _videoControllers.values) {
      controller.dispose();
    }
    for (final controller in _chewieControllers.values) {
      controller.dispose();
    }

    // Reset system UI and orientation
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
    ]);

    _pageController.dispose();
    _controlsTimer?.cancel();
    super.dispose();
  }

  void _startControlsTimer() {
    _controlsTimer?.cancel();
    _controlsTimer = Timer(const Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          _showControls = false;
        });
      }
    });
  }

  Future<void> _initializeVideoPlayer(int index) async {
    if (index < 0 ||
        index >= widget.reels.length ||
        _videoControllers.containsKey(index)) {
      return;
    }

    try {
      // Handle web platform differently
      if (kIsWeb) {
        setState(() {
          _errorMessages[index] =
              'Video playback is not available in web demo.';
        });
        return;
      }

      final reel = widget.reels[index];
      final file = File(reel.downloadPath);

      if (await file.exists()) {
        // Check if this is a placeholder file by checking file size
        final fileSize = await file.length();

        // If file is very small (less than 10KB), it might be a placeholder text file
        if (fileSize < 10 * 1024) {
          try {
            final content = await file.readAsString();
            if (content.contains('This is a placeholder')) {
              setState(() {
                _errorMessages[index] =
                    'This is a simulated download from a third-party API.';
              });
              return;
            }
          } catch (e) {
            debugPrint('Could not read file as string, trying as video: $e');
          }
        }

        // This is a real video file
        final videoController = VideoPlayerController.file(file);
        _videoControllers[index] = videoController;

        await videoController.initialize();

        if (videoController.value.duration.inMilliseconds == 0) {
          setState(() {
            _errorMessages[index] = 'Invalid video file or unsupported format.';
          });
          return;
        }

        // Create the chewie controller
        final chewieController = ChewieController(
          videoPlayerController: videoController,
          autoPlay: index == _currentIndex, // Only autoplay the current video
          looping: true,
          aspectRatio: videoController.value.aspectRatio > 0
              ? videoController.value.aspectRatio
              : 16 / 9,
          errorBuilder: (context, errorMessage) {
            return Center(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  'Error playing video: $errorMessage',
                  style: const TextStyle(color: Colors.white),
                  textAlign: TextAlign.center,
                ),
              ),
            );
          },
          allowFullScreen: true,
          allowMuting: true,
          showControls: false, // We'll use our custom controls
          placeholder: Center(
            child: SpinKitPulse(
              color: Theme.of(context).primaryColor,
              size: 50.0,
            ),
          ),
        );

        _chewieControllers[index] = chewieController;

        setState(() {
          _isInitialized[index] = true;
        });
      } else {
        setState(() {
          _errorMessages[index] = 'Video file not found';
        });
      }
    } catch (e) {
      setState(() {
        _errorMessages[index] = 'Error initializing video player: $e';
      });
      debugPrint('Error initializing video player: $e');
    }
  }

  void _onPageChanged(int index) {
    setState(() {
      _currentIndex = index;
    });

    // Pause the previous video
    if (_videoControllers.containsKey(_currentIndex - 1)) {
      _videoControllers[_currentIndex - 1]?.pause();
    }

    // Play the current video
    if (_videoControllers.containsKey(_currentIndex)) {
      _videoControllers[_currentIndex]?.play();
    }

    // Preload the next video if it's not already loaded
    if (index < widget.reels.length - 1 &&
        !_videoControllers.containsKey(index + 1)) {
      _initializeVideoPlayer(index + 1);
    }

    // Preload the previous video if it's not already loaded
    if (index > 0 && !_videoControllers.containsKey(index - 1)) {
      _initializeVideoPlayer(index - 1);
    }
  }

  void _toggleControls() {
    setState(() {
      _showControls = !_showControls;
    });

    if (_showControls) {
      _startControlsTimer();
    }
  }

  void _seekForward() {
    if (_videoControllers.containsKey(_currentIndex)) {
      final controller = _videoControllers[_currentIndex]!;
      final position = controller.value.position;
      final duration = controller.value.duration;

      final newPosition = position + const Duration(seconds: 10);
      if (newPosition < duration) {
        controller.seekTo(newPosition);
      } else {
        controller.seekTo(duration);
      }
    }
  }

  void _seekBackward() {
    if (_videoControllers.containsKey(_currentIndex)) {
      final controller = _videoControllers[_currentIndex]!;
      final position = controller.value.position;

      final newPosition = position - const Duration(seconds: 10);
      if (newPosition > Duration.zero) {
        controller.seekTo(newPosition);
      } else {
        controller.seekTo(Duration.zero);
      }
    }
  }

  void _adjustVolume(double delta) {
    setState(() {
      _volume = (_volume + delta).clamp(0.0, 1.0);
      if (_videoControllers.containsKey(_currentIndex)) {
        _videoControllers[_currentIndex]!.setVolume(_volume);
      }
    });

    _showControls = true;
    _startControlsTimer();
  }

  void _adjustBrightness(double delta) {
    setState(() {
      _brightness = (_brightness + delta).clamp(0.0, 1.0);
      // In a real app, you would adjust the screen brightness here
    });

    _showControls = true;
    _startControlsTimer();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: GestureDetector(
        onTap: _toggleControls,
        onDoubleTapDown: (details) {
          final screenWidth = MediaQuery.of(context).size.width;
          if (details.globalPosition.dx < screenWidth / 2) {
            _seekBackward();
          } else {
            _seekForward();
          }
        },
        onVerticalDragUpdate: (details) {
          final screenWidth = MediaQuery.of(context).size.width;
          if (details.globalPosition.dx < screenWidth / 2) {
            // Left side - adjust brightness
            _adjustBrightness(-details.delta.dy * 0.01);
          } else {
            // Right side - adjust volume
            _adjustVolume(-details.delta.dy * 0.01);
          }
        },
        child: Stack(
          children: [
            // Video player
            PageView.builder(
              controller: _pageController,
              itemCount: widget.reels.length,
              onPageChanged: _onPageChanged,
              itemBuilder: (context, index) {
                return _buildVideoPlayer(index);
              },
            ),

            // Custom controls overlay
            if (_showControls) _buildControlsOverlay(),
          ],
        ),
      ),
    );
  }

  Widget _buildVideoPlayer(int index) {
    if (_isInitialized[index] == true &&
        _chewieControllers.containsKey(index)) {
      return Chewie(controller: _chewieControllers[index]!);
    } else if (_errorMessages.containsKey(index)) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Text(
            _errorMessages[index]!,
            style: const TextStyle(color: Colors.white),
            textAlign: TextAlign.center,
          ),
        ),
      );
    } else {
      return Center(
        child: SpinKitPulse(
          color: Theme.of(context).primaryColor,
          size: 50.0,
        ),
      );
    }
  }

  Widget _buildControlsOverlay() {
    final reel = widget.reels[_currentIndex];
    final videoController = _videoControllers[_currentIndex];

    return Container(
      color: Colors.black.withOpacity(0.4),
      child: Column(
        children: [
          // Top bar with close button and video info
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '@${reel.username}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close, color: Colors.white),
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            ),
          ),

          const Spacer(),

          // Bottom controls
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                // Progress bar
                if (videoController != null)
                  ValueListenableBuilder<VideoPlayerValue>(
                    valueListenable: videoController,
                    builder: (context, value, child) {
                      return Column(
                        children: [
                          // Progress bar
                          SliderTheme(
                            data: SliderThemeData(
                              trackHeight: 2,
                              thumbShape: const RoundSliderThumbShape(
                                enabledThumbRadius: 6,
                              ),
                              overlayShape: const RoundSliderOverlayShape(
                                overlayRadius: 12,
                              ),
                              activeTrackColor: Theme.of(context).primaryColor,
                              inactiveTrackColor: Colors.white30,
                              thumbColor: Theme.of(context).primaryColor,
                              overlayColor: Theme.of(context)
                                  .primaryColor
                                  .withOpacity(0.3),
                            ),
                            child: Slider(
                              value: value.position.inMilliseconds.toDouble(),
                              min: 0,
                              max: value.duration.inMilliseconds.toDouble(),
                              onChanged: (newValue) {
                                videoController.seekTo(
                                  Duration(milliseconds: newValue.toInt()),
                                );
                              },
                            ),
                          ),

                          // Time display
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                _formatDuration(value.position),
                                style: const TextStyle(color: Colors.white),
                              ),
                              Text(
                                _formatDuration(value.duration),
                                style: const TextStyle(color: Colors.white),
                              ),
                            ],
                          ),
                        ],
                      );
                    },
                  ),

                const SizedBox(height: 16),

                // Playback controls
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    IconButton(
                      icon: const Icon(Icons.replay_10,
                          color: Colors.white, size: 32),
                      onPressed: _seekBackward,
                    ),
                    IconButton(
                      icon: Icon(
                        videoController?.value.isPlaying ?? false
                            ? Icons.pause
                            : Icons.play_arrow,
                        color: Colors.white,
                        size: 48,
                      ),
                      onPressed: () {
                        if (videoController != null) {
                          videoController.value.isPlaying
                              ? videoController.pause()
                              : videoController.play();
                          setState(() {});
                        }
                      },
                    ),
                    IconButton(
                      icon: const Icon(Icons.forward_10,
                          color: Colors.white, size: 32),
                      onPressed: _seekForward,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = twoDigits(duration.inHours);
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));

    return duration.inHours > 0
        ? '$hours:$minutes:$seconds'
        : '$minutes:$seconds';
  }
}
