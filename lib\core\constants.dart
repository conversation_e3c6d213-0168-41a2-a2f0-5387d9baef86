import 'package:flutter/material.dart';

/// App-wide constants and configuration
class AppConstants {
  // App Information
  static const String appName = 'Instagram Reels Downloader';
  static const String appVersion = '2.0.0';
  
  // File and Directory Constants
  static const String downloadFolderName = 'Instagram Reels';
  static const String dcimFolderName = 'DCIM/Instagram Reels';
  static const String downloadsFolderName = 'Download/Instagram Reels';
  
  // File naming patterns
  static const String fileNamePattern = '{username}_{date}_{id}';
  static const String dateFormat = 'yyyy-MM-dd_HH-mm-ss';
  
  // Network and API
  static const Duration networkTimeout = Duration(seconds: 30);
  static const Duration downloadTimeout = Duration(minutes: 10);
  static const int maxRetryAttempts = 3;
  static const Duration retryDelay = Duration(seconds: 2);
  
  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double borderRadius = 12.0;
  static const double cardElevation = 2.0;
  
  // Animation durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 400);
  static const Duration longAnimation = Duration(milliseconds: 600);
  
  // Video player constants
  static const double videoAspectRatio = 16 / 9;
  static const Duration videoSeekDuration = Duration(seconds: 10);
  
  // Download queue constants
  static const int maxConcurrentDownloads = 3;
  static const int maxQueueSize = 50;
  
  // Storage keys
  static const String themeModeKey = 'theme_mode';
  static const String primaryColorKey = 'primary_color';
  static const String downloadedReelsKey = 'downloaded_reels';
  static const String downloadQueueKey = 'download_queue';
  static const String settingsKey = 'app_settings';
  
  // Error messages
  static const String networkErrorMessage = 'Network connection error. Please check your internet connection.';
  static const String permissionErrorMessage = 'Storage permission is required to download videos.';
  static const String invalidUrlErrorMessage = 'Please enter a valid Instagram Reel URL.';
  static const String downloadFailedMessage = 'Failed to download video. Please try again.';
  static const String fileNotFoundMessage = 'Downloaded file not found.';
  
  // Success messages
  static const String downloadSuccessMessage = 'Video downloaded successfully!';
  static const String shareSuccessMessage = 'Video shared successfully!';
  static const String copySuccessMessage = 'Link copied to clipboard!';
  
  // Instagram URL patterns
  static const List<String> instagramUrlPatterns = [
    'instagram.com/reel/',
    'instagram.com/p/',
    'instagram.com/tv/',
    'instagr.am/reel/',
    'instagr.am/p/',
  ];
  
  // Supported video formats
  static const List<String> supportedVideoFormats = [
    '.mp4',
    '.mov',
    '.avi',
    '.mkv',
    '.webm',
  ];
  
  // File size limits (in bytes)
  static const int maxVideoFileSize = 500 * 1024 * 1024; // 500MB
  static const int minVideoFileSize = 1024; // 1KB
  
  // Sharing platforms
  static const Map<String, String> sharingPlatforms = {
    'whatsapp': 'WhatsApp',
    'facebook': 'Facebook',
    'twitter': 'Twitter',
    'telegram': 'Telegram',
    'email': 'Email',
    'copyLink': 'Copy Link',
    'more': 'More Options',
  };
}

/// App color constants
class AppColors {
  // Primary colors
  static const Color primaryColor = Color(0xFFE91E63); // Pink
  static const Color primaryVariant = Color(0xFFC2185B);
  static const Color secondaryColor = Color(0xFF03DAC6);
  static const Color secondaryVariant = Color(0xFF018786);
  
  // Background colors
  static const Color lightBackground = Color(0xFFFAFAFA);
  static const Color darkBackground = Color(0xFF121212);
  static const Color lightSurface = Color(0xFFFFFFFF);
  static const Color darkSurface = Color(0xFF1E1E1E);
  
  // Text colors
  static const Color lightTextPrimary = Color(0xFF212121);
  static const Color lightTextSecondary = Color(0xFF757575);
  static const Color darkTextPrimary = Color(0xFFFFFFFF);
  static const Color darkTextSecondary = Color(0xFFB3B3B3);
  
  // Status colors
  static const Color successColor = Color(0xFF4CAF50);
  static const Color errorColor = Color(0xFFF44336);
  static const Color warningColor = Color(0xFFFF9800);
  static const Color infoColor = Color(0xFF2196F3);
  
  // Social media colors
  static const Color whatsappColor = Color(0xFF25D366);
  static const Color facebookColor = Color(0xFF1877F2);
  static const Color twitterColor = Color(0xFF1DA1F2);
  static const Color telegramColor = Color(0xFF0088CC);
  static const Color instagramColor = Color(0xFFE4405F);
  
  // Gradient colors
  static const List<Color> primaryGradient = [
    Color(0xFFE91E63),
    Color(0xFFF06292),
  ];
  
  static const List<Color> backgroundGradient = [
    Color(0xFFFAFAFA),
    Color(0xFFF5F5F5),
  ];
}

/// App text styles
class AppTextStyles {
  // Headings
  static const TextStyle heading1 = TextStyle(
    fontSize: 32,
    fontWeight: FontWeight.bold,
    letterSpacing: -0.5,
  );
  
  static const TextStyle heading2 = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.bold,
    letterSpacing: -0.25,
  );
  
  static const TextStyle heading3 = TextStyle(
    fontSize: 20,
    fontWeight: FontWeight.w600,
    letterSpacing: 0,
  );
  
  // Body text
  static const TextStyle bodyLarge = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.normal,
    letterSpacing: 0.15,
  );
  
  static const TextStyle bodyMedium = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.normal,
    letterSpacing: 0.25,
  );
  
  static const TextStyle bodySmall = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.normal,
    letterSpacing: 0.4,
  );
  
  // Button text
  static const TextStyle buttonText = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w600,
    letterSpacing: 0.75,
  );
  
  // Caption text
  static const TextStyle caption = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.normal,
    letterSpacing: 0.4,
  );
}
