import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:instagram_reels_downloader/models/reel_model.dart';
import 'package:instagram_reels_downloader/providers/reels_provider.dart';
import 'package:instagram_reels_downloader/screens/video_player_screen.dart';
import 'package:instagram_reels_downloader/widgets/share_options_widget.dart';
import 'package:provider/provider.dart';

class DownloadsScreen extends StatefulWidget {
  const DownloadsScreen({Key? key}) : super(key: key);

  @override
  State<DownloadsScreen> createState() => DownloadsScreenState();
}

class DownloadsScreenState extends State<DownloadsScreen> {
  @override
  void initState() {
    super.initState();
    // Refresh the downloaded reels list
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<ReelsProvider>(context, listen: false).loadDownloadedReels();
    });
  }

  // Share a downloaded reel
  void _shareReel(ReelModel reel) async {
    // Show share options
    ShareOptionsWidget.show(context, reel);
  }

  // Delete a downloaded reel
  void _deleteReel(ReelModel reel) async {
    // For web, we handle differently
    if (kIsWeb) {
      // In web, we can just delete from storage without file operations
      final reelsProvider = Provider.of<ReelsProvider>(context, listen: false);

      // Show confirmation dialog
      final confirmed = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Delete Reel'),
          content: const Text('Are you sure you want to delete this reel?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context, true),
              style: TextButton.styleFrom(foregroundColor: Colors.red),
              child: const Text('Delete'),
            ),
          ],
        ),
      );

      if (confirmed == true && mounted) {
        // Delete the reel
        await reelsProvider.deleteReel(reel.id);
      }
      return;
    }

    // For mobile platforms
    // Get provider before async gap
    final reelsProvider = Provider.of<ReelsProvider>(context, listen: false);

    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Reel'),
        content: const Text('Are you sure you want to delete this reel?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true && mounted) {
      // Delete the reel
      await reelsProvider.deleteReel(reel.id);

      // Delete the file
      try {
        final file = File(reel.downloadPath);
        if (await file.exists()) {
          await file.delete();
        }
      } catch (e) {
        // Use a logger in production code
        debugPrint('Error deleting file: $e');
      }
    }
  }

  // Clear all downloaded reels
  void _clearAllDownloads() async {
    // Get provider before async gap
    final reelsProvider = Provider.of<ReelsProvider>(context, listen: false);
    final reels = reelsProvider.downloadedReels;

    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Downloads'),
        content:
            const Text('Are you sure you want to delete all downloaded reels?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete All'),
          ),
        ],
      ),
    );

    if (confirmed == true && mounted) {
      // For web, we skip file operations
      if (!kIsWeb) {
        // Delete all files
        for (final reel in reels) {
          try {
            final file = File(reel.downloadPath);
            if (await file.exists()) {
              await file.delete();
            }
          } catch (e) {
            // Use a logger in production code
            debugPrint('Error deleting file: $e');
          }
        }
      }

      // Clear all reels from storage
      await reelsProvider.clearDownloadedReels();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Downloaded Reels'),
        actions: [
          IconButton(
            icon: const Icon(Icons.delete_forever),
            onPressed: () => _clearAllDownloads(),
            tooltip: 'Clear All Downloads',
          ),
        ],
      ),
      body: Consumer<ReelsProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (provider.downloadedReels.isEmpty) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.download_done,
                    size: 64,
                    color: Colors.grey,
                  ),
                  SizedBox(height: 16),
                  Text(
                    'No downloaded reels yet',
                    style: TextStyle(
                      fontSize: 18,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            );
          }

          return ListView.builder(
            itemCount: provider.downloadedReels.length,
            itemBuilder: (context, index) {
              final reel = provider.downloadedReels[index];
              return Card(
                margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: ListTile(
                  leading: reel.thumbnailUrl.isNotEmpty
                      ? ClipRRect(
                          borderRadius: BorderRadius.circular(4),
                          child: Image.network(
                            reel.thumbnailUrl,
                            width: 60,
                            height: 60,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                width: 60,
                                height: 60,
                                color: Colors.grey,
                                child: const Icon(Icons.video_file),
                              );
                            },
                          ),
                        )
                      : Container(
                          width: 60,
                          height: 60,
                          color: Colors.grey,
                          child: const Icon(Icons.video_file),
                        ),
                  title: Text(
                    '@${reel.username}',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (reel.caption.isNotEmpty)
                        Text(
                          reel.caption.length > 50
                              ? '${reel.caption.substring(0, 50)}...'
                              : reel.caption,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      const SizedBox(height: 4),
                      Text(
                        'Downloaded on ${reel.downloadDate.day}/${reel.downloadDate.month}/${reel.downloadDate.year}',
                        style: const TextStyle(fontSize: 12),
                      ),
                    ],
                  ),
                  trailing: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        icon: const Icon(Icons.share),
                        onPressed: () => _shareReel(reel),
                        tooltip: 'Share',
                      ),
                      IconButton(
                        icon: const Icon(Icons.delete),
                        onPressed: () => _deleteReel(reel),
                        tooltip: 'Delete',
                      ),
                    ],
                  ),
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => VideoPlayerScreen(
                          reels: provider.downloadedReels,
                          initialIndex: index,
                        ),
                      ),
                    );
                  },
                ),
              );
            },
          );
        },
      ),
    );
  }
}
