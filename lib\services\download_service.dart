import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:instagram_reels_downloader/core/extensions.dart';
import 'package:instagram_reels_downloader/models/reel_model.dart';
import 'package:instagram_reels_downloader/services/instagram_service.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';

class DownloadService {
  final Dio _dio = Dio();

  // Request storage permissions
  Future<bool> requestPermissions() async {
    // Skip permission check for web
    if (kIsWeb) {
      return true;
    }

    // For Android
    if (Platform.isAndroid) {
      // For Android 10 and below
      if (await Permission.storage.request().isGranted) {
        debugPrint('Storage permission granted');
        return true;
      }

      // For Android 11+, try to request all possible permissions
      try {
        // Request manage external storage permission (needed for Android 11+)
        if (await Permission.manageExternalStorage.request().isGranted) {
          debugPrint('Manage external storage permission granted');
          return true;
        }
      } catch (e) {
        debugPrint('Error requesting manage external storage permission: $e');
      }

      // Try media permissions as fallback
      try {
        if (await Permission.videos.request().isGranted) {
          debugPrint('Videos permission granted');
          return true;
        }
      } catch (e) {
        debugPrint('Error requesting videos permission: $e');
      }

      // Check if any permission is already granted
      if (await Permission.storage.status.isGranted ||
          await Permission.manageExternalStorage.status.isGranted ||
          await Permission.videos.status.isGranted) {
        debugPrint('Some storage permission is already granted');
        return true;
      }

      debugPrint('All storage permissions denied');
      return false;
    }

    // For iOS
    if (Platform.isIOS) {
      // iOS handles permissions differently, but we'll request photos permission
      final status = await Permission.photos.request();
      debugPrint('iOS photos permission status: ${status.isGranted}');
      return status.isGranted;
    }

    // For other platforms
    return true;
  }

  // Get the download directory
  Future<String> getDownloadDirectory() async {
    if (kIsWeb) {
      // For web, we don't have a file system access
      // We'll use a mock path for demonstration
      return '/downloads/Instagram Reels';
    }

    if (Platform.isAndroid) {
      // Try multiple approaches for Android

      // Approach 1: Try to use the public Downloads directory (Android 11+)
      if (await Permission.manageExternalStorage.status.isGranted) {
        try {
          // Use the public Downloads directory
          final downloadsDir =
              Directory('/storage/emulated/0/Download/Instagram Reels');

          // Create the directory if it doesn't exist
          if (!await downloadsDir.exists()) {
            await downloadsDir.create(recursive: true);
            debugPrint('Created public directory: ${downloadsDir.path}');
          } else {
            debugPrint('Public directory already exists: ${downloadsDir.path}');
          }

          // Verify the directory is writable
          final testFile = File('${downloadsDir.path}/test.txt');
          await testFile.writeAsString('Test file');
          await testFile.delete();
          debugPrint('Public directory is writable');

          return downloadsDir.path;
        } catch (e) {
          debugPrint('Error using public Downloads directory: $e');
        }
      }

      // Approach 2: Try to use the DCIM directory (often accessible)
      try {
        final dcimDir = Directory('/storage/emulated/0/DCIM/Instagram Reels');

        // Create the directory if it doesn't exist
        if (!await dcimDir.exists()) {
          await dcimDir.create(recursive: true);
          debugPrint('Created DCIM directory: ${dcimDir.path}');
        } else {
          debugPrint('DCIM directory already exists: ${dcimDir.path}');
        }

        // Verify the directory is writable
        try {
          final testFile = File('${dcimDir.path}/test.txt');
          await testFile.writeAsString('Test file');
          await testFile.delete();
          debugPrint('DCIM directory is writable');

          return dcimDir.path;
        } catch (e) {
          debugPrint('DCIM directory is not writable: $e');
        }
      } catch (e) {
        debugPrint('Error using DCIM directory: $e');
      }

      // Approach 3: Fallback to app-specific directory (always works but not visible to other apps)
      try {
        final directory = await getExternalStorageDirectory();
        if (directory != null) {
          final downloadsDir = Directory('${directory.path}/Instagram Reels');

          // Create the directory if it doesn't exist
          if (!await downloadsDir.exists()) {
            await downloadsDir.create(recursive: true);
            debugPrint('Created app-specific directory: ${downloadsDir.path}');
          } else {
            debugPrint(
                'App-specific directory already exists: ${downloadsDir.path}');
          }

          return downloadsDir.path;
        } else {
          throw Exception('Could not get external storage directory');
        }
      } catch (e) {
        debugPrint('Error using app-specific directory: $e');

        // Last resort: use temporary directory
        final tempDir = await getTemporaryDirectory();
        final downloadsDir = Directory('${tempDir.path}/Instagram Reels');

        if (!await downloadsDir.exists()) {
          await downloadsDir.create(recursive: true);
        }

        debugPrint(
            'Using temporary directory as last resort: ${downloadsDir.path}');
        return downloadsDir.path;
      }
    } else if (Platform.isIOS) {
      // For iOS, we use the Documents directory
      try {
        final directory = await getApplicationDocumentsDirectory();
        final downloadsDir = Directory('${directory.path}/Instagram Reels');

        // Create the directory if it doesn't exist
        if (!await downloadsDir.exists()) {
          await downloadsDir.create(recursive: true);
          debugPrint('Created iOS directory: ${downloadsDir.path}');
        } else {
          debugPrint('iOS directory already exists: ${downloadsDir.path}');
        }

        return downloadsDir.path;
      } catch (e) {
        debugPrint('Error creating iOS directory: $e');

        // Fallback to temporary directory
        final tempDir = await getTemporaryDirectory();
        final downloadsDir = Directory('${tempDir.path}/Instagram Reels');

        if (!await downloadsDir.exists()) {
          await downloadsDir.create(recursive: true);
        }

        return downloadsDir.path;
      }
    } else {
      // For other platforms (desktop)
      try {
        final directory = await getApplicationDocumentsDirectory();
        final downloadsDir = Directory('${directory.path}/Instagram Reels');

        // Create the directory if it doesn't exist
        if (!await downloadsDir.exists()) {
          await downloadsDir.create(recursive: true);
        }

        return downloadsDir.path;
      } catch (e) {
        debugPrint('Error creating directory for other platform: $e');

        // Fallback to temporary directory
        final tempDir = await getTemporaryDirectory();
        final downloadsDir = Directory('${tempDir.path}/Instagram Reels');

        if (!await downloadsDir.exists()) {
          await downloadsDir.create(recursive: true);
        }

        return downloadsDir.path;
      }
    }
  }

  // Download a reel
  Future<ReelModel?> downloadReel(
    ReelModel reel, {
    Function(int received, int total)? onProgress,
  }) async {
    try {
      // Request permissions
      final hasPermission = await requestPermissions();
      if (!hasPermission) {
        throw Exception('Storage permission denied');
      }

      // Get the download directory
      final downloadDir = await getDownloadDirectory();

      // Create a user-friendly filename with username and timestamp
      // Remove any invalid characters from the username
      final sanitizedUsername = reel.username.sanitizedFilename;

      // Create a formatted date string
      final now = DateTime.now();
      final dateStr = now.filenameFormat;

      // Create a unique filename with username and date
      final fileName = '${sanitizedUsername}_${dateStr}_${reel.id}.mp4';
      final filePath = '$downloadDir/$fileName';

      // For web, we can't download files directly
      if (kIsWeb) {
        // In a real app, you would implement a way to download the file in the browser
        // For this demo, we'll just simulate a successful download
        await Future.delayed(
            const Duration(seconds: 2)); // Simulate download time

        // Update the reel with the mock download path and simulated file size
        final updatedReel = ReelModel(
          id: reel.id,
          videoUrl: reel.videoUrl,
          thumbnailUrl: reel.thumbnailUrl,
          username: reel.username,
          caption: reel.caption,
          downloadPath: filePath, // Mock path
          downloadDate: DateTime.now(),
          fileSize: 15 * 1024 * 1024, // Simulate a 15MB file
          downloadStatus: 'completed',
        );

        return updatedReel;
      }

      // For mobile platforms, download the file
      try {
        // Import the InstagramService to check if the URL is a direct video URL
        final instagramService = InstagramService();

        // Check if the URL is a direct video URL or a fallback URL
        if (!instagramService.isDirectVideoUrl(reel.videoUrl) &&
            reel.videoUrl.contains('instagram.com/reel/')) {
          // This is not a direct video URL but an Instagram page URL
          throw Exception(
              'Direct video URL not found. Instagram may have changed their API or HTML structure.');
        }

        // If it's a mock URL from our third-party API service (for demo purposes)
        if (reel.videoUrl.contains('mock-instagram-cdn.com')) {
          // In a real app, this would be a real video URL from the third-party API
          // For demo purposes, we'll simulate a successful download
          await Future.delayed(const Duration(seconds: 2));
          debugPrint(
              'Simulating download of video from third-party API: ${reel.videoUrl}');

          // For mock URLs, we'll create a placeholder file instead of trying to download
          // In a real app, you would download from the actual URL
          final file = File(filePath);
          await file.writeAsString(
              'This is a placeholder for a downloaded video from ${reel.videoUrl}');
          debugPrint('Created placeholder file for mock URL');
        } else if (reel.videoUrl.contains('example.com/mock_video.mp4')) {
          // This is a mock URL from the web version
          debugPrint('Mock URL detected from web version: ${reel.videoUrl}');

          // Create a placeholder file
          final file = File(filePath);
          await file.writeAsString(
              'This is a placeholder for a downloaded video from ${reel.videoUrl}');
          debugPrint('Created placeholder file for web mock URL');
        } else {
          // This is a direct video URL, we can download it
          debugPrint('Downloading real video from: ${reel.videoUrl}');
          debugPrint('Saving to: $filePath');

          // Verify the directory exists
          final directory =
              Directory(filePath.substring(0, filePath.lastIndexOf('/')));
          if (!await directory.exists()) {
            debugPrint(
                'Directory does not exist, creating it: ${directory.path}');
            await directory.create(recursive: true);
          }

          // Verify we can write to the directory
          try {
            final testFile = File('${directory.path}/test_write.txt');
            await testFile.writeAsString('Test write');
            await testFile.delete();
            debugPrint('Directory is writable: ${directory.path}');
          } catch (e) {
            debugPrint(
                'Directory is not writable: ${directory.path}, error: $e');
            throw Exception('Directory is not writable: ${directory.path}');
          }

          try {
            debugPrint('Starting download with Dio...');
            await _dio.download(
              reel.videoUrl,
              filePath,
              onReceiveProgress: (received, total) {
                if (total != -1) {
                  // Calculate download progress
                  final progress = (received / total * 100).toStringAsFixed(0);
                  debugPrint('Download progress: $progress%');

                  // Call the progress callback if provided
                  onProgress?.call(received, total);
                }
              },
              options: Options(
                headers: {
                  'User-Agent':
                      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                  'Accept': '*/*',
                  'Referer': 'https://www.instagram.com/',
                },
                // Add a longer timeout for large videos
                receiveTimeout: const Duration(minutes: 5),
                // Ensure we're handling binary data properly
                responseType: ResponseType.bytes,
                followRedirects: true,
                validateStatus: (status) {
                  return status != null && status < 500;
                },
              ),
            );

            // Verify the downloaded file
            final file = File(filePath);
            if (await file.exists()) {
              final fileSize = await file.length();
              debugPrint('Downloaded file size: $fileSize bytes');

              // We'll continue regardless of file size, as some valid videos might be small
              // and we don't want to block the user from accessing their downloads
            }
          } catch (downloadError) {
            debugPrint('Error during download: $downloadError');

            // Try an alternative download method if Dio fails
            try {
              debugPrint(
                  'Trying alternative download method with HttpClient...');

              // Verify the directory exists again
              final directory =
                  Directory(filePath.substring(0, filePath.lastIndexOf('/')));
              if (!await directory.exists()) {
                debugPrint(
                    'Directory does not exist, creating it: ${directory.path}');
                await directory.create(recursive: true);
              }

              final httpClient = HttpClient();
              httpClient.connectionTimeout = const Duration(seconds: 30);

              debugPrint('Creating request to: ${reel.videoUrl}');
              final request = await httpClient.getUrl(Uri.parse(reel.videoUrl));

              // Add headers
              request.headers.add('User-Agent',
                  'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
              request.headers.add('Accept', '*/*');
              request.headers.add('Referer', 'https://www.instagram.com/');

              debugPrint('Sending request...');
              final response = await request.close();
              debugPrint(
                  'Received response with status code: ${response.statusCode}');

              if (response.statusCode == 200) {
                debugPrint('Response is OK, starting download...');
                final file = File(filePath);
                final sink = file.openWrite();

                // Track progress
                int received = 0;
                final total = response.contentLength;
                debugPrint('Content length: $total bytes');

                await response.forEach((bytes) {
                  sink.add(bytes);
                  received += bytes.length;

                  if (total > 0) {
                    final progress =
                        (received / total * 100).toStringAsFixed(0);
                    debugPrint('Alternative download progress: $progress%');

                    // Call the progress callback if provided
                    onProgress?.call(received, total);
                  }
                });

                await sink.close();
                debugPrint('Alternative download completed successfully');

                // Verify the file exists and has content
                if (await file.exists()) {
                  final fileSize = await file.length();
                  debugPrint('Downloaded file size: $fileSize bytes');

                  if (fileSize == 0) {
                    throw Exception('Downloaded file is empty');
                  }
                } else {
                  throw Exception('File does not exist after download');
                }
              } else {
                throw Exception(
                    'Failed to download: HTTP ${response.statusCode}');
              }
            } catch (alternativeError) {
              debugPrint(
                  'Alternative download method also failed: $alternativeError');
              throw Exception('All download methods failed: $alternativeError');
            }
          }
        }
      } catch (e) {
        // If download fails, throw the error to be handled by the caller
        debugPrint('Error downloading video: $e');
        throw Exception('Failed to download video: $e');
      }

      // Get the file size
      int? fileSize;
      try {
        final file = File(filePath);
        if (await file.exists()) {
          fileSize = await file.length();
        }
      } catch (e) {
        debugPrint('Error getting file size: $e');
      }

      // Add metadata to the file (for supported platforms)
      await _addMetadataToFile(filePath, reel);

      // Update the reel with the download path and file size
      final updatedReel = ReelModel(
        id: reel.id,
        videoUrl: reel.videoUrl,
        thumbnailUrl: reel.thumbnailUrl,
        username: reel.username,
        caption: reel.caption,
        downloadPath: filePath,
        downloadDate: DateTime.now(),
        fileSize: fileSize,
        downloadStatus: 'completed',
      );

      return updatedReel;
    } catch (e) {
      debugPrint('Error downloading reel: $e');
      return null;
    }
  }

  // Add metadata to the downloaded video file
  Future<void> _addMetadataToFile(String filePath, ReelModel reel) async {
    try {
      // This is a placeholder for adding metadata to video files
      // In a real implementation, you would use platform-specific methods
      // or a plugin to add metadata to the video file

      // For Android, you could use MediaStore API to add metadata
      if (Platform.isAndroid && !kIsWeb) {
        // Example of how you might add metadata on Android
        // This is just a placeholder and would need a proper implementation
        debugPrint('Adding metadata to video file: $filePath');
        debugPrint('Title: ${reel.username}\'s Instagram Reel');
        debugPrint('Author: ${reel.username}');
        debugPrint('Description: ${reel.caption}');

        // In a real implementation, you would use MediaStore or other APIs
        // to add this metadata to the video file
      }

      // For iOS, you could use the Photos framework
      if (Platform.isIOS && !kIsWeb) {
        // Example of how you might add metadata on iOS
        debugPrint('Adding metadata to video file: $filePath');
        // In a real implementation, you would use the Photos framework
        // to add metadata to the video file
      }
    } catch (e) {
      debugPrint('Error adding metadata to file: $e');
      // Don't throw an error, as this is not critical functionality
    }
  }
}
